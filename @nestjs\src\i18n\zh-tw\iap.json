{"productNotFound": "未找到產品", "purchaseSuccess": "購買成功完成", "paymentFailed": "支付失敗", "dailyTypeLimitReached": "已達到{productType}產品的每日購買限制。您每天只能購買一個{productType}產品。", "dailyLimitReached": "已達到產品的每日購買限制：{productName}", "accountLimitReached": "已達到產品的賬戶購買限制：{productName}", "vipAlreadyActive": "VIP會員已激活", "priceNotAvailable": "所選支付方式不可用於產品：{productName}", "walletIdRequired": "需要錢包ID", "missingRequiredParameters": "缺少必需參數", "walletNotFound": "未找到錢包", "dappPortalConfigMissing": "DappPortal配置缺失", "userWalletAddressNotFound": "未找到用戶錢包地址", "failedToCreatePaymentOrder": "創建支付訂單失敗", "dappPortalUnavailable": "DappPortal服務不可用", "invalidPaymentResponse": "支付服務響應無效", "walletIdAndBoosterIdRequired": "需要錢包ID和道具ID"}