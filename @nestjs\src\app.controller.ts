import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { I18nLang, I18nTranslate } from './common/decorators';
import { CustomI18nService, SupportedLanguage } from './i18n/i18n.service';
import { TestValidationDto } from './common/dto/test-validation.dto';
import { I18nBadRequestException, I18nNotFoundException } from './common/exceptions/i18n.exception';

@ApiTags('Application')
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly i18nService: CustomI18nService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get application info' })
  @ApiResponse({ status: 200, description: 'Application information' })
  getHello(
    @I18nLang() language: string,
    @I18nTranslate() t: (key: string, options?: any) => string,
  ): any {
    return {
      message: this.appService.getHello(),
      translatedMessage: t('common.success'),
      language,
      timestamp: new Date().toISOString(),
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Application health status' })
  getHealth(@I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    const statusMessage = this.i18nService.translate('common.success', {}, lang);

    return {
      status: 'ok',
      statusMessage,
      timestamp: new Date().toISOString(),
      service: 'Wolf.Fun NestJS API',
      language,
      version: '2.0',
      i18n: {
        supportedLanguages: ['en', 'zh', 'zh-tw', 'ja'],
        currentLanguage: language,
      },
    };
  }

  @Get('i18n-test')
  @ApiOperation({ summary: 'Test i18n functionality' })
  @ApiResponse({ status: 200, description: 'I18n test results' })
  testI18n(@I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    const testKeys = [
      'common.success',
      'common.error',
      'common.not_found',
      'errors.unauthorized',
      'tasks.dailySignin',
    ];

    const translations = testKeys.reduce((acc, key) => {
      acc[key] = this.i18nService.translate(key, {}, lang);
      return acc;
    }, {} as Record<string, string>);

    return {
      language,
      translations,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('test-validation')
  @ApiOperation({ summary: 'Test validation with i18n' })
  @ApiResponse({ status: 200, description: 'Validation test successful' })
  @ApiResponse({ status: 400, description: 'Validation failed' })
  testValidation(@Body() dto: TestValidationDto, @I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    return {
      message: this.i18nService.translate('common.success', {}, lang),
      data: dto,
      language,
    };
  }

  @Get('test-error')
  @ApiOperation({ summary: 'Test i18n error handling' })
  @ApiResponse({ status: 400, description: 'Test error' })
  testError() {
    // 抛出一个i18n异常来测试错误处理
    throw new I18nBadRequestException('errors.walletNotFound');
  }

  @Get('test-not-found')
  @ApiOperation({ summary: 'Test i18n not found error' })
  @ApiResponse({ status: 404, description: 'Not found error' })
  testNotFound() {
    throw new I18nNotFoundException('errors.userNotFound');
  }
}
