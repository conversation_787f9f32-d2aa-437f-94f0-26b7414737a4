'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('开始执行迁移：将 TON 和 Telegram 相关字段设置为可选...');

      // 1. 修改 Users 表 - 将 Telegram 相关字段设置为可选
      console.log('修改 Users 表字段...');
      
      // 检查字段是否存在，如果存在则修改为可选
      const usersTableInfo = await queryInterface.describeTable('Users');
      
      if (usersTableInfo.telegramId) {
        await queryInterface.changeColumn('Users', 'telegramId', {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false // 移除唯一约束，因为可能有多个 NULL 值
        }, { transaction });
        console.log('✓ Users.telegramId 字段已设置为可选');
      }

      if (usersTableInfo.hasFollowedChannel) {
        await queryInterface.changeColumn('Users', 'hasFollowedChannel', {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: null
        }, { transaction });
        console.log('✓ Users.hasFollowedChannel 字段已设置为可选');
      }

      if (usersTableInfo.authDate) {
        await queryInterface.changeColumn('Users', 'authDate', {
          type: Sequelize.INTEGER,
          allowNull: true
        }, { transaction });
        console.log('✓ Users.authDate 字段已设置为可选');
      }

      if (usersTableInfo.hash) {
        await queryInterface.changeColumn('Users', 'hash', {
          type: Sequelize.STRING,
          allowNull: true
        }, { transaction });
        console.log('✓ Users.hash 字段已设置为可选');
      }

      if (usersTableInfo.telegram_premium) {
        await queryInterface.changeColumn('Users', 'telegram_premium', {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: null
        }, { transaction });
        console.log('✓ Users.telegram_premium 字段已设置为可选');
      }

      // 2. 修改 UserWallets 表 - 将 TON 字段设置为可选
      console.log('修改 UserWallets 表字段...');
      
      const userWalletsTableInfo = await queryInterface.describeTable('UserWallets');
      
      if (userWalletsTableInfo.ton) {
        await queryInterface.changeColumn('UserWallets', 'ton', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: true,
          defaultValue: null
        }, { transaction });
        console.log('✓ UserWallets.ton 字段已设置为可选');
      }

      // 3. 修改 PaymentRequests 表 - 将 telegramId 字段设置为可选
      console.log('修改 PaymentRequests 表字段...');
      
      const paymentRequestsTableInfo = await queryInterface.describeTable('PaymentRequests');
      
      if (paymentRequestsTableInfo.telegramId) {
        await queryInterface.changeColumn('PaymentRequests', 'telegramId', {
          type: Sequelize.STRING,
          allowNull: true
        }, { transaction });
        console.log('✓ PaymentRequests.telegramId 字段已设置为可选');
      }

      // 4. 修改 ChestBoosts 表 - 将 targetTelegramId 字段设置为可选（如果存在）
      console.log('检查 ChestBoosts 表字段...');
      
      try {
        const chestBoostsTableInfo = await queryInterface.describeTable('ChestBoosts');
        
        if (chestBoostsTableInfo.targetTelegramId) {
          await queryInterface.changeColumn('ChestBoosts', 'targetTelegramId', {
            type: Sequelize.STRING,
            allowNull: true
          }, { transaction });
          console.log('✓ ChestBoosts.targetTelegramId 字段已设置为可选');
        }
      } catch (error) {
        console.log('ChestBoosts 表不存在或字段不存在，跳过...');
      }

      await transaction.commit();
      console.log('✅ 迁移完成：所有 TON 和 Telegram 相关字段已设置为可选');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('开始回滚迁移：恢复 TON 和 Telegram 相关字段为必填...');

      // 注意：回滚操作需要谨慎，因为可能存在 NULL 值
      // 这里只提供基本的回滚逻辑，实际使用时可能需要先清理 NULL 值

      // 1. 恢复 Users 表字段
      const usersTableInfo = await queryInterface.describeTable('Users');
      
      if (usersTableInfo.telegramId) {
        // 警告：回滚前需要确保没有 NULL 值
        console.log('警告：回滚 Users.telegramId 字段，请确保没有 NULL 值');
        await queryInterface.changeColumn('Users', 'telegramId', {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true
        }, { transaction });
      }

      if (usersTableInfo.hasFollowedChannel) {
        await queryInterface.changeColumn('Users', 'hasFollowedChannel', {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        }, { transaction });
      }

      if (usersTableInfo.authDate) {
        await queryInterface.changeColumn('Users', 'authDate', {
          type: Sequelize.INTEGER,
          allowNull: false
        }, { transaction });
      }

      if (usersTableInfo.hash) {
        await queryInterface.changeColumn('Users', 'hash', {
          type: Sequelize.STRING,
          allowNull: false
        }, { transaction });
      }

      if (usersTableInfo.telegram_premium) {
        await queryInterface.changeColumn('Users', 'telegram_premium', {
          type: Sequelize.BOOLEAN,
          allowNull: true // 这个字段原本就是可选的
        }, { transaction });
      }

      // 2. 恢复 UserWallets 表字段
      const userWalletsTableInfo = await queryInterface.describeTable('UserWallets');
      
      if (userWalletsTableInfo.ton) {
        await queryInterface.changeColumn('UserWallets', 'ton', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0
        }, { transaction });
      }

      // 3. 恢复 PaymentRequests 表字段
      const paymentRequestsTableInfo = await queryInterface.describeTable('PaymentRequests');
      
      if (paymentRequestsTableInfo.telegramId) {
        await queryInterface.changeColumn('PaymentRequests', 'telegramId', {
          type: Sequelize.STRING,
          allowNull: true // 这个字段原本就是可选的
        }, { transaction });
      }

      await transaction.commit();
      console.log('✅ 回滚完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 回滚失败:', error);
      throw error;
    }
  }
};
