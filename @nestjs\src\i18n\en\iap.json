{"productNotFound": "Product not found", "purchaseSuccess": "Purchase completed successfully", "paymentFailed": "Payment failed", "dailyTypeLimitReached": "Daily purchase limit reached for {productType} products. You can only purchase one {productType} product per day.", "dailyLimitReached": "Daily purchase limit reached for product: {productName}", "accountLimitReached": "Account purchase limit reached for product: {productName}", "vipAlreadyActive": "VIP membership already active", "priceNotAvailable": "Price not available for selected payment method for product: {productName}", "walletIdRequired": "Wallet ID is required", "missingRequiredParameters": "Missing required parameters", "walletNotFound": "Wallet not found", "dappPortalConfigMissing": "DappPortal configuration missing", "userWalletAddressNotFound": "User wallet address not found", "failedToCreatePaymentOrder": "Failed to create payment order", "dappPortalUnavailable": "DappPortal service unavailable", "invalidPaymentResponse": "Invalid payment service response", "walletIdAndBoosterIdRequired": "Wallet ID and booster ID are required"}