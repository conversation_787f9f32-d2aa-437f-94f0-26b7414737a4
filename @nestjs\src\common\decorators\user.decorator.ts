import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { RequestUser } from '../services/request-context.service';

export const User = createParamDecorator(
  (data: keyof RequestUser | undefined, ctx: ExecutionContext): RequestUser | any => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    return data ? user?.[data] : user;
  },
);

export const CurrentUser = User;
export const GetUser = User;
