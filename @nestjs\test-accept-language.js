#!/usr/bin/env node

/**
 * 测试 Accept-Language 头的语言检测功能
 * 验证 nestjs-i18n 配置是否正确工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试用例
const testCases = [
  {
    name: '英语 (en)',
    headers: { 'Accept-Language': 'en-US,en;q=0.9' },
    expectedLang: 'en'
  },
  {
    name: '中文简体 (zh)',
    headers: { 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8' },
    expectedLang: 'zh'
  },
  {
    name: '中文繁体 (zh-tw)',
    headers: { 'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8' },
    expectedLang: 'zh-tw'
  },
  {
    name: '日语 (ja)',
    headers: { 'Accept-Language': 'ja-JP,ja;q=0.9,en;q=0.8' },
    expectedLang: 'ja'
  },
  {
    name: '不支持的语言 (fr) - 应回退到英语',
    headers: { 'Accept-Language': 'fr-FR,fr;q=0.9' },
    expectedLang: 'en'
  },
  {
    name: '无 Accept-Language 头 - 应回退到英语',
    headers: {},
    expectedLang: 'en'
  }
];

async function testAcceptLanguage() {
  console.log('🌍 测试 Accept-Language 头的语言检测功能...\n');

  // 检查服务器是否运行
  try {
    await axios.get(`${BASE_URL}/health`);
  } catch (error) {
    console.log('❌ 服务器未运行，请先启动应用: npm run start:dev');
    process.exit(1);
  }

  let allTestsPassed = true;

  for (const testCase of testCases) {
    console.log(`📝 测试: ${testCase.name}`);
    console.log('----------------------------------------');

    try {
      // 测试 i18n-test 端点
      const response = await axios.get(`${BASE_URL}/i18n-test`, {
        headers: testCase.headers
      });

      const { language, translations } = response.data;
      
      console.log(`   检测到的语言: ${language}`);
      console.log(`   期望的语言: ${testCase.expectedLang}`);
      
      // 检查语言是否正确
      if (language === testCase.expectedLang) {
        console.log('✅ 语言检测正确');
      } else {
        console.log('❌ 语言检测错误');
        allTestsPassed = false;
      }

      // 显示一些翻译示例
      console.log('   翻译示例:');
      console.log(`     common.success: ${translations['common.success']}`);
      console.log(`     errors.unauthorized: ${translations['errors.unauthorized']}`);

    } catch (error) {
      console.log(`❌ 测试失败: ${error.message}`);
      allTestsPassed = false;
    }

    console.log('');
  }

  if (allTestsPassed) {
    console.log('🎉 所有 Accept-Language 测试通过！');
    console.log('✅ nestjs-i18n 配置正确，语言检测功能正常工作');
  } else {
    console.log('❌ 部分测试失败，请检查配置');
    process.exit(1);
  }
}

// 运行测试
testAcceptLanguage().catch(error => {
  console.error('测试执行失败:', error.message);
  process.exit(1);
});
