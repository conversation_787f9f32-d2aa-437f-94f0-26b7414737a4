import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { I18nLang } from '../common/decorators';
import { CustomI18nService } from '../i18n/i18n.service';
import { I18nBadRequestException, I18nNotFoundException } from '../common/exceptions/i18n.exception';

/**
 * I18n 使用示例控制器
 * 演示如何在实际业务中使用多语言功能
 */
@ApiTags('I18n Examples')
@Controller('examples/i18n')
export class I18nExampleController {
  constructor(private readonly i18nService: CustomI18nService) {}

  @Get('user/:id')
  @ApiOperation({ summary: 'Get user by ID (with i18n)' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUser(@Param('id') id: string, @I18nLang() language: string) {
    // 模拟用户查找
    const userId = parseInt(id);
    
    if (isNaN(userId) || userId <= 0) {
      throw new I18nBadRequestException('errors.paramValidation');
    }

    // 模拟用户不存在
    if (userId === 999) {
      throw new I18nNotFoundException('errors.userNotFound');
    }

    const lang = this.i18nService.findBestMatchingLanguage(language);
    
    return {
      id: userId,
      name: 'John Doe',
      message: this.i18nService.translate('common.success', {}, lang),
      language,
    };
  }

  @Get('wallet/:walletId/balance')
  @ApiOperation({ summary: 'Get wallet balance (with i18n)' })
  @ApiParam({ name: 'walletId', description: 'Wallet ID' })
  @ApiResponse({ status: 200, description: 'Balance retrieved' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async getWalletBalance(@Param('walletId') walletId: string, @I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    
    // 模拟钱包不存在
    if (walletId === 'invalid') {
      throw new I18nNotFoundException('errors.walletNotFound');
    }

    return {
      walletId,
      balance: {
        moof: 1000,
        milk: 500,
        gem: 50,
      },
      message: this.i18nService.translate('common.success', {}, lang),
      language,
    };
  }

  @Post('chest/open')
  @ApiOperation({ summary: 'Open chests (with i18n and variables)' })
  @ApiResponse({ status: 200, description: 'Chests opened successfully' })
  @ApiResponse({ status: 400, description: 'Not enough chests' })
  async openChests(
    @Body() body: { count: number; available: number },
    @I18nLang() language: string,
  ) {
    const { count, available } = body;
    const lang = this.i18nService.findBestMatchingLanguage(language);

    if (count <= 0) {
      throw new I18nBadRequestException('errors.chestOpenCountInvalid');
    }

    if (count > available) {
      // 使用变量插值的错误消息
      const errorMessage = this.i18nService.translate(
        'errors.notEnoughChests',
        { required: count, available },
        lang,
      );
      
      throw new I18nBadRequestException('errors.notEnoughChests', { required: count, available });
    }

    return {
      opened: count,
      remaining: available - count,
      message: this.i18nService.translate('common.success', {}, lang),
      language,
    };
  }

  @Get('tasks')
  @ApiOperation({ summary: 'Get tasks list (with i18n)' })
  @ApiQuery({ name: 'type', required: false, description: 'Task type filter' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved' })
  async getTasks(@Query('type') type: string, @I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);

    const tasks = [
      {
        id: 1,
        type: 'daily',
        name: this.i18nService.translate('tasks.dailySignin', {}, lang),
        completed: false,
      },
      {
        id: 2,
        type: 'social',
        name: this.i18nService.translate('tasks.followX', {}, lang),
        completed: true,
      },
      {
        id: 3,
        type: 'social',
        name: this.i18nService.translate('tasks.joinTelegram', {}, lang),
        completed: false,
      },
      {
        id: 4,
        type: 'referral',
        name: this.i18nService.translate('tasks.inviteFriends', {}, lang),
        completed: false,
      },
    ];

    // 根据类型过滤
    const filteredTasks = type ? tasks.filter(task => task.type === type) : tasks;

    return {
      tasks: filteredTasks,
      total: filteredTasks.length,
      message: this.i18nService.translate('common.success', {}, lang),
      language,
    };
  }

  @Get('error-examples')
  @ApiOperation({ summary: 'Error handling examples' })
  @ApiQuery({ name: 'errorType', description: 'Type of error to simulate' })
  @ApiResponse({ status: 400, description: 'Various error types' })
  async errorExamples(@Query('errorType') errorType: string) {
    switch (errorType) {
      case 'unauthorized':
        throw new I18nBadRequestException('errors.unauthorized');
      case 'insufficient-balance':
        throw new I18nBadRequestException('errors.insufficientBalance');
      case 'transaction-failed':
        throw new I18nBadRequestException('errors.transactionFailed');
      case 'daily-limit':
        throw new I18nBadRequestException('errors.dailyLimitReached');
      default:
        throw new I18nBadRequestException('errors.paramValidation');
    }
  }

  @Get('translation-demo')
  @ApiOperation({ summary: 'Translation demonstration' })
  @ApiResponse({ status: 200, description: 'Translation examples' })
  async translationDemo(@I18nLang() language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);

    return {
      language,
      examples: {
        common: {
          success: this.i18nService.translate('common.success', {}, lang),
          error: this.i18nService.translate('common.error', {}, lang),
          notFound: this.i18nService.translate('common.not_found', {}, lang),
        },
        tasks: {
          dailySignin: this.i18nService.translate('tasks.dailySignin', {}, lang),
          inviteFriends: this.i18nService.translate('tasks.inviteFriends', {}, lang),
          followX: this.i18nService.translate('tasks.followX', {}, lang),
        },
        errors: {
          unauthorized: this.i18nService.translate('errors.unauthorized', {}, lang),
          walletNotFound: this.i18nService.translate('errors.walletNotFound', {}, lang),
          insufficientBalance: this.i18nService.translate('errors.insufficientBalance', {}, lang),
        },
        withVariables: {
          notEnoughChests: this.i18nService.translate(
            'errors.notEnoughChests',
            { required: 5, available: 2 },
            lang,
          ),
        },
      },
    };
  }
}
