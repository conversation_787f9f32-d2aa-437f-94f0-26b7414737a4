#!/usr/bin/env node

/**
 * 完整的 Web3 登录测试
 * 包括生成真实的钱包、签名和验证整个登录流程
 */

const axios = require('axios');
const { ethers } = require('ethers');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

async function testCompleteWeb3Login() {
  console.log('🔐 完整的 Web3 登录测试...\n');

  try {
    // 步骤1: 生成测试钱包
    console.log('1. 生成测试钱包...');
    const wallet = ethers.Wallet.createRandom();
    console.log(`✅ 钱包生成成功`);
    console.log(`   地址: ${wallet.address}`);
    console.log(`   私钥: ${wallet.privateKey}`);
    console.log('');

    // 步骤2: 获取nonce
    console.log('2. 获取nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: wallet.address
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    if (!nonceResponse.data.ok) {
      console.log('❌ 获取nonce失败');
      console.log('响应:', JSON.stringify(nonceResponse.data, null, 2));
      return;
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取nonce成功`);
    console.log(`   Nonce: ${nonce}`);
    console.log(`   消息: ${message.substring(0, 50)}...`);
    console.log('');

    // 步骤3: 使用钱包签名消息
    console.log('3. 签名消息...');
    const signature = await wallet.signMessage(message);
    console.log(`✅ 消息签名成功`);
    console.log(`   签名: ${signature.substring(0, 20)}...`);
    console.log('');

    // 步骤4: 验证签名（本地验证）
    console.log('4. 本地验证签名...');
    try {
      const recoveredAddress = ethers.verifyMessage(message, signature);
      const isValidLocal = recoveredAddress.toLowerCase() === wallet.address.toLowerCase();
      console.log(`✅ 本地签名验证: ${isValidLocal ? '成功' : '失败'}`);
      console.log(`   恢复的地址: ${recoveredAddress}`);
      console.log(`   原始地址: ${wallet.address}`);
      console.log('');
    } catch (error) {
      console.log('❌ 本地签名验证失败:', error.message);
      return;
    }

    // 步骤5: 测试登录
    console.log('5. 测试登录...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: wallet.address,
      signature: signature,
      message: message,
      referralCode: undefined
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    console.log('登录响应:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.ok) {
      console.log('✅ 登录成功！');
      console.log(`   用户ID: ${loginResponse.data.data.user.id}`);
      console.log(`   用户名: ${loginResponse.data.data.user.username}`);
      console.log(`   钱包地址: ${loginResponse.data.data.user.walletAddress}`);
      console.log(`   Token: ${loginResponse.data.data.token.substring(0, 20)}...`);
    } else {
      console.log('❌ 登录失败');
      console.log(`   错误消息: ${loginResponse.data.message}`);
      console.log(`   错误详情: ${loginResponse.data.error}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function testWithSpecificWallet() {
  console.log('\n🔐 使用特定钱包测试（开发环境）...\n');

  try {
    // 使用开发环境的测试钱包地址
    const testWalletAddress = '******************************************';
    
    console.log('1. 使用测试钱包地址...');
    console.log(`   地址: ${testWalletAddress}`);
    console.log('');

    // 获取nonce
    console.log('2. 获取nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: testWalletAddress
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    if (!nonceResponse.data.ok) {
      console.log('❌ 获取nonce失败');
      return;
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取nonce成功: ${nonce}`);
    console.log('');

    // 测试登录（使用任意签名，开发环境应该跳过验证）
    console.log('3. 测试登录（开发环境）...');
    const testSignature = '******************************************90abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
    
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: testWalletAddress,
      signature: testSignature,
      message: message,
      referralCode: undefined
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    console.log('登录响应:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.ok) {
      console.log('✅ 开发环境登录成功！');
    } else {
      console.log('❌ 开发环境登录失败');
      console.log(`   错误: ${loginResponse.data.error}`);
    }

  } catch (error) {
    console.error('❌ 开发环境测试失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function checkEnvironment() {
  console.log('🔍 检查环境配置...\n');
  
  try {
    // 检查健康状态
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 服务健康检查通过');
    console.log('健康状态:', JSON.stringify(healthResponse.data, null, 2));
  } catch (error) {
    console.log('❌ 服务健康检查失败');
    console.log('请确保应用正在运行: npm run start:dev');
    return false;
  }
  
  return true;
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始 Web3 登录完整测试\n');
  
  // 检查环境
  const isHealthy = await checkEnvironment();
  if (!isHealthy) {
    process.exit(1);
  }
  
  console.log('\n' + '='.repeat(60));
  
  // 测试1: 完整的真实签名流程
  await testCompleteWeb3Login();
  
  console.log('\n' + '='.repeat(60));
  
  // 测试2: 开发环境特定钱包测试
  await testWithSpecificWallet();
  
  console.log('\n🎉 所有测试完成！');
}

// 运行测试
runAllTests().catch(error => {
  console.error('测试执行失败:', error.message);
  process.exit(1);
});
