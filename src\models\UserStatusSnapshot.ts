import { DataTypes, Model } from "sequelize";
import { sequelize } from "../config/db";

export interface UserStatusSnapshotAttributes {
  id?: number;
  userId: number;
  walletId: number;
  goldenBullCount: number; // 快照时的变身金牛次数
  snapshotDate: Date; // 快照时间
}

export class UserStatusSnapshot extends Model<UserStatusSnapshotAttributes> implements UserStatusSnapshotAttributes {
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public goldenBullCount!: number;
  public snapshotDate!: Date;
}

UserStatusSnapshot.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    goldenBullCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    snapshotDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: "user_status_snapshots",
    sequelize,
  }
);

export default UserStatusSnapshot;