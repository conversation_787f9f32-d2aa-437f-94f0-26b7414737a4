# NestJS Application Configuration
NODE_ENV=development
NESTJS_PORT=3001

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=wolf_fun

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_PASS=
REDIS_DB=0

# Logging
LOG_LEVEL=debug

# Web3 Testing Configuration
TEST_ETH_PRIVATE_KEY=your-test-ethereum-private-key-here
