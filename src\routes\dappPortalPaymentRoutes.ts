import { Router } from "express";
import { handleLockEvent, handleUnlockEvent, handleStatusChangeEvent } from "../controllers/dappPortalPaymentController";

const router = Router();

// Dapp Portal Webhook 回调接口
// @ts-ignore 忽略异步处理器类型检查
router.post("/lock", handleLockEvent);
// @ts-ignore
router.post("/unlock", handleUnlockEvent);
// @ts-ignore
router.post("/status", handleStatusChangeEvent);

export default router; 