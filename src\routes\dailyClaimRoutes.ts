// src/routes/dailyClaimRoutes.ts
import { Router, Request, Response } from "express";
import { claimDailyBox } from "../services/dailyRewardService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义领取每日宝箱请求体验证模式
const claimDailySchema = {
  type: "object",
  properties: {},
  additionalProperties: false
};

const validateClaimDaily = ajv.compile(claimDailySchema);

/**
 * POST /api/invite/claim-daily
 */
router.post(
  "/claim-daily",
  walletAuthMiddleware,
  //@ts-ignore
  async (req: Request, res: Response) => {
    try {
      const myReq = req as MyRequest;
      const { userId, walletId } = myReq.user!;

      // 验证请求体
      const valid = validateClaimDaily(req.body);
      if (!valid) {
        return res.status(400).json(errorResponse(
          tFromRequest(req, "errors.paramValidation"),
          formatValidationErrors(validateClaimDaily.errors || [], req.language)
        ));
      }

      if (!userId) {
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserId")));
      }

      const result = await claimDailyBox(userId, walletId!);
      res.json(successResponse(
        { 
          dailyChests: result.dailyChests,
          rewards: result.rewards 
        },
        tFromRequest(req, "success.dailyChestsClaimed", { count: result.dailyChests })
      ));
    } catch (error: any) {
      res.status(400).json(errorResponse(error.message));
    }
  }
);

export default router;
