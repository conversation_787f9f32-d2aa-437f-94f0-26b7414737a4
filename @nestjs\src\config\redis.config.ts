import { registerAs } from '@nestjs/config';

export default registerAs('redis', () => ({
  // Redis 连接配置
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  password: process.env.REDIS_PASSWORD || process.env.REDIS_PASS || '',
  db: parseInt(process.env.REDIS_DB, 10) || 0,
  
  // 连接选项
  options: {
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxRetriesPerRequest: null,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
    
    // 重试策略
    retryStrategy: (times: number) => {
      const delay = Math.min(times * 100, 3000);
      console.log(`[Redis] 连接重试次数: ${times}, 延迟: ${delay}ms`);
      return delay;
    },
    
    // 重连错误处理
    reconnectOnError: (err: Error) => {
      console.error('[Redis] 连接错误:', err.message);
      const targetError = 'READONLY';
      return err.message.includes(targetError);
    },
  },
  
  // 队列配置
  queue: {
    defaultJobOptions: {
      removeOnComplete: 10,
      removeOnFail: 50,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
    
    // 队列名称列表
    queues: [
      'lottery-result-job',
      'moof-holders-reward-job',
      'personal-kol-reward-job',
      'team-kol-reward-job',
      'daily-rebate-settlement-job',
      'jackpot-chest-queue',
      'withdrawal-queue',
      'kaia-price-update-job',
    ],
  },
}));
