import { Job, Worker } from "bullmq";
import {
  Room,
  UserWallet,
  Reservation,
  GameHistory,
  WalletHistory,
  Session,
  PrizePool,
  User, // 新增导入奖池模型
  BullUnlockHistory,
  BullKingRecord,
  Announcement,
  UserStatusSnapshot
} from "../models";
import { redis } from "../config/redis";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { Op, Transaction } from "sequelize";
import { sequelize } from "../config/db";
import { GAME_CONFIG,TICKET_REBATE_CONFIG } from "../config/consts";
import dotenv from "dotenv";

// 导入返利处理函数
import { processTicketRebate } from "../services/ticketRebateService";

dayjs.extend(utc);
dayjs.extend(timezone);
const TZ = "Asia/Shanghai";

dotenv.config();


const BULL_REWARD_SHARE = {
  1: 0.05, // 级别1 5%奖励
  2: 0.03, // 级别2 3%奖励
  3: 0.02, // 级别3 2%奖励
};

// 服务函数：处理金牛奖励分享
async function handleBullRewardShare(winner: { userId: number; walletId: number }, transaction: Transaction) {
  let currentUserId = winner.userId;

  for (let level = 1; level <= 3; level++) {
    const referrer = await User.findOne({
      where: { id: currentUserId },
      transaction,
    });

    if (!referrer || !referrer.referrerId) {
      logger.info(`玩家 ${currentUserId} 没有上级推荐人，跳过级别 ${level}`);
      break;
    }

    const referrerId = referrer.referrerId;
    const sharePercentage = BULL_REWARD_SHARE[level as keyof typeof BULL_REWARD_SHARE];
    const rewardAmount = REWARDS.WIN * sharePercentage; // 计算该级别推荐人的奖励金额

    const totalRewardAmount = REWARDS.WIN;
    // 解锁 6.3% 的金额
    const unlockedAmount = totalRewardAmount * 0.063; // 解锁的 6.3% 计算

    // 为推荐人发放奖励
    const referrerWallet = await UserWallet.findOne({
      where: { userId: referrerId },
      transaction,
    });

    if (referrerWallet) {
      await UserWallet.increment("moof", { by: rewardAmount - unlockedAmount, where: { id: referrerWallet.id }, transaction });
      await WalletHistory.create(
        {
          userId: referrerId,
          walletId: referrerWallet.id,
          amount: rewardAmount,
          currency: "moof",
          reference: "Golden Bull Sharing Bonus",
          action: "in",
          category: "moof",
          credit_type: "moof",
          fe_display_remark: `金牛奖励分享 级别 ${level}`,
          developer_remark: `金牛奖励分享给推荐人 ${referrerId}，金额 ${rewardAmount}`,
        },
        { transaction }
      );

      //更新userwallet 表的 unlockMoof 字段
      await UserWallet.increment("unlockMoof", { by: unlockedAmount, where: { id: referrerWallet.id }, transaction });

      // 创建解锁历史并解锁 6.3% 的金额
      await BullUnlockHistory.create(
        {
          userId: referrerId,
          unlockDate: new Date(),
          totalAmount: rewardAmount,
          availableAmount: unlockedAmount, // 立即解锁的 6.3% 金额
          withdrawnAmount: 0.0, // 初始提现金额为 0
        },
        { transaction }
      );

      logger.info(`为推荐人 ${referrerId} 发放金牛奖励分享，金额: ${rewardAmount}`);
    } else {
      logger.warn(`未找到推荐人钱包，用户 ID: ${referrerId}`);
    }

    // 更新 currentUserId 为上一层级的推荐人，继续处理上级
    currentUserId = referrer.referrerId;
  }
}

// 日志工具
const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[LotteryWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[LotteryWorker] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[LotteryWorker] ${message}`, ...args);
  },
};

logger.info("模块初始化开始...");

async function initializePrizePools() {
  const prizePoolTypes = [
    { type: 'bull_king', amount: 0.0 },
    { type: 'moof_holders', amount: 0.0 },
    { type: 'personal_kol', amount: 0.0 },
    { type: 'team_kol', amount: 0.0 },
  ];

  for (const prize of prizePoolTypes) {
    // 检查是否已经存在该类型的奖池，如果不存在则创建
    await PrizePool.findOrCreate({
      where: { type: prize.type },
      defaults: {
        type: prize.type,
        amount: prize.amount
      },
    });
  }
  logger.info("奖池初始化完成");
}

(async () => {
  await initializePrizePools();
})();

// 奖励配置
const REWARDS = {
  WIN: 2000, // 游戏胜利奖励 2000 $MOOF
  FAIL: 103, // 游戏失败奖励 103 USD
  MAX_PARTICIPANTS: GAME_CONFIG.MAX_PARTICIPANTS, // 房间人数上限
  USD_COST: GAME_CONFIG.USD_COST, // 退款时返还的 USD 数额
  TICKET_COST: GAME_CONFIG.TICKET_COST, // 退款时返还的门票数量
};

// 辅助函数：根据 session_category 判断场次编号
const getSessionIndex = (session_category: string) =>
  session_category === "12:00:00" ? 1 : 2;

/**
 * 服务函数：处理退款逻辑
 */
async function handleRefund(
  room: any,
  sessionId: number,
  roundIndex: number,
  session_category: string,
  transaction: Transaction
) {
  logger.info(`开始为房间 ${room.id} 处理退款`);
  const reservations = await Reservation.findAll({
    where: {
      roomId: room.id,
      sessionId,
      roundIndex,
      status: "reserved",
    },
    transaction,
  });

  if (!reservations.length) {
    logger.info(`房间 ${room.id} 没有需要退款的预约记录`);
    return;
  }

  const refundPromises = reservations.map(async (reservation) => {
    try {
      const wallet = await UserWallet.findByPk(reservation.walletId, { transaction });
      if (!wallet) {
        logger.warn(`未找到钱包，预约 ID: ${reservation.id}，房间: ${room.id}`);
        return;
      }

      // 根据ticketType决定退还哪种类型的票
      const ticketType = reservation.ticketType || 'ticket'; // 默认为普通票
      const ticketField = ticketType === 'free_ticket' ? 'free_ticket' : 'ticket';
      
      logger.info(`为钱包 ${wallet.id} 退还 ${ticketType} 类型的门票`);      
      
      const operations = [
        UserWallet.increment("usd", { by: REWARDS.USD_COST, where: { id: wallet.id }, transaction }),
        UserWallet.increment(ticketField, { by: REWARDS.TICKET_COST, where: { id: wallet.id }, transaction }),
        Reservation.update(
          { status: "refunded" },
          { where: { roomId: room.id, walletId: wallet.id, sessionId, roundIndex }, transaction }
        ),
        WalletHistory.create(
          {
            userId: Number(wallet.userId),
            walletId: Number(wallet.id),
            amount: REWARDS.USD_COST,
            currency: "usd",
            reference: "Refund",
            action: "in",
            category: "usd",
            credit_type: "usd",
            fe_display_remark: "Refund",
            developer_remark: `退款 USD，场次: ${getSessionIndex(session_category)}，回合: ${roundIndex}`,
          },
          { transaction }
        ),
        WalletHistory.create(
          {
            userId: Number(wallet.userId),
            walletId: Number(wallet.id),
            amount: REWARDS.TICKET_COST,
            currency: ticketType,
            reference: "Refund",
            action: "in",
            category: ticketType,
            credit_type: ticketType,
            fe_display_remark: "Refund",
            developer_remark: `退款${ticketType === 'free_ticket' ? '免费' : ''}门票，场次: ${getSessionIndex(session_category)}，回合: ${roundIndex}`,
          },
          { transaction }
        ),
        GameHistory.update(
          { game_status: "refunded", payout_status: "refunded", payout: 0 },
          { where: { roomId: room.id, walletId: wallet.id, sessionId: sessionId, round: roundIndex }, transaction }
        ),
      ];

      const results = await Promise.allSettled(operations);
      for (const result of results) {
        if (result.status === "rejected") {
          logger.error(`退款操作失败，钱包: ${wallet.id}，房间: ${room.id}`, result.reason);
          throw new Error(`退款操作失败，钱包: ${wallet.id}`);
        }
      }
      logger.info(`退款成功，钱包: ${wallet.id}，房间: ${room.id}`);
    } catch (error) {
      logger.error(`处理退款时出错，预约 ID: ${reservation.id}，房间: ${room.id}:`, error);
      throw error;
    }
  });

  const settled = await Promise.allSettled(refundPromises);
  for (const res of settled) {
    if (res.status === "rejected") {
      throw new Error("一个或多个退款操作失败");
    }
  }
  logger.info(`房间 ${room.id} 的退款处理完成`);
}

/**
 * 服务函数：处理奖励发放逻辑
 */
// 牛王奖励配置
const BULL_KING_REWARDS = {
  LEVEL_5: 0.5,  // 5次变身获得50%奖池
  LEVEL_4: 0.1,  // 4次变身瓜分10%奖池
  LEVEL_3: 0.1,  // 3次变身瓜分10%奖池
};

// 处理牛王奖励分配
async function handleBullKingRewards(winner: { userId: number; walletId: number }, transaction: Transaction) {
  // 获取玩家的金牛变身记录
  const bullKingRecord = await BullKingRecord.findOne({
    where: { walletId: winner.walletId },
    transaction,
  });

  if (!bullKingRecord) {
    // 如果没有记录，创建新记录
    await BullKingRecord.create({
      userId: winner.userId,
      walletId: winner.walletId,
      transformCount: 1,
    }, { transaction });
    
    // 更新 Redis 排行榜
    await redis.zadd('bull_king_leaderboard', 1, winner.walletId.toString());
    logger.info(`更新牛王排行榜：新玩家 ${winner.walletId} 的变身次数为 1`);
    return;
  }

  // 增加变身次数
  await bullKingRecord.increment('transformCount', { transaction });
  await bullKingRecord.reload({ transaction });

  // 更新 Redis 排行榜
  await redis.zadd('bull_king_leaderboard', bullKingRecord.transformCount, bullKingRecord.walletId.toString());
  logger.info(`更新牛王排行榜：玩家 ${bullKingRecord.walletId} 的变身次数为 ${bullKingRecord.transformCount}`);

  // 获取当前牛王宝座奖池金额
  const bullKingPool = await PrizePool.findOne({
    where: { type: 'bull_king' },
    transaction,
  });

  if (!bullKingPool || bullKingPool.amount <= 0) {
    return;
  }

  // 根据变身次数处理奖励
  if (bullKingRecord.transformCount === 5) {
    // 处理5次变身的玩家
    const rewardAmount5 = bullKingPool.amount * BULL_KING_REWARDS.LEVEL_5;
    const wallet5 = await UserWallet.findOne({
      where: { id: winner.walletId },
      transaction,
    });

    if (wallet5) {
      await UserWallet.increment('bullReward', { 
        by: rewardAmount5, 
        where: { id: wallet5.id }, 
        transaction 
      });

      // 创建牛王称号公告
      await Announcement.create({
        userId: winner.userId,
        walletId: winner.walletId,
        amount: rewardAmount5,
        currency: 'usd',
        title: '新的牛王诞生！',
        content: `恭喜玩家 ID: ${winner.userId} 成功变身金牛5次，获得牛王称号！奖励金额：${rewardAmount5}`,
        type: 'bull_king',
      }, { transaction });

      logger.info(`玩家 ${winner.userId} 获得牛王称号，奖励金额: ${rewardAmount5}`);
    }

    // 处理4次变身的玩家
    const players4 = await BullKingRecord.findAll({
      where: { transformCount: 4 },
      transaction,
    });

    if (players4.length > 0) {
      const totalReward4 = bullKingPool.amount * BULL_KING_REWARDS.LEVEL_4;
      const rewardAmount4 = totalReward4 / players4.length;

      for (const player of players4) {
        const wallet = await UserWallet.findOne({
          where: { id: player.walletId },
          transaction,
        });

        if (wallet) {
          await UserWallet.increment('bullReward', {
            by: rewardAmount4,
            where: { id: wallet.id },
            transaction,
          });

          logger.info(`玩家 ${player.userId} 获得金牛变身4次奖励，金额: ${rewardAmount4}`);
        }
      }
    }

    // 处理3次变身的玩家
    const players3 = await BullKingRecord.findAll({
      where: { transformCount: 3 },
      transaction,
    });

    if (players3.length > 0) {
      const totalReward3 = bullKingPool.amount * BULL_KING_REWARDS.LEVEL_3;
      const rewardAmount3 = totalReward3 / players3.length;

      for (const player of players3) {
        const wallet = await UserWallet.findOne({
          where: { userId: player.userId },
          transaction,
        });

        if (wallet) {
          await UserWallet.increment('bullReward', {
            by: rewardAmount3,
            where: { id: wallet.id },
            transaction,
          });

          logger.info(`玩家 ${player.userId} 获得金牛变身3次奖励，金额: ${rewardAmount3}`);
        }
      }
    }

    // 更新奖池金额
    const totalUsedAmount = rewardAmount5 + 
      (players4.length > 0 ? bullKingPool.amount * BULL_KING_REWARDS.LEVEL_4 : 0) +
      (players3.length > 0 ? bullKingPool.amount * BULL_KING_REWARDS.LEVEL_3 : 0);

    await bullKingPool.decrement('amount', {
      by: totalUsedAmount,
      transaction,
    });

    // 在重置前，保存所有玩家的变身次数快照
    const allBullKingRecords = await BullKingRecord.findAll({
      where: { transformCount: { [Op.gt]: 0 } },
      transaction,
    });

    // 创建快照记录
    for (const record of allBullKingRecords) {
      await UserStatusSnapshot.create({
        userId: record.userId,
        walletId: record.walletId,
        goldenBullCount: record.transformCount,
        snapshotDate: new Date(),
      }, { transaction });
    }

    // 重置所有玩家的变身次数
    await BullKingRecord.update(
      { transformCount: 0 },
      { where: {}, transaction }
    );
    
    // 重置 Redis 中的牛王排行榜
    await redis.del('bull_king_leaderboard');
    logger.info('牛王周期结束，已重置 Redis 中的牛王排行榜');
  }
}

async function handleRewards(
  room: any,
  winner: { walletId: number; userId: number; player_index: number },
  playerWinRates: { walletId: number; userId: number; player_index: number }[],
  sessionId: number,
  roundIndex: number,
  session_category: string,
  transaction: Transaction
) {
  logger.info(`开始为房间 ${room.id} 处理奖励发放，获胜者: 钱包 ID=${winner.walletId}，座位号=${winner.player_index}`);

  // 胜利者奖励
  const winnerOperations = [
    UserWallet.increment("moof", { by: REWARDS.WIN - REWARDS.WIN * 0.063, where: { id: winner.walletId }, transaction }),
    Reservation.update(
      { status: "success" },
      { where: { roomId: room.id, walletId: winner.walletId, sessionId, roundIndex }, transaction }
    ),

    // 更新userWallet的unlockMoof字段
    UserWallet.increment("unlockMoof", { by: REWARDS.WIN * 0.063, where: { id: winner.walletId }, transaction }),

    await BullUnlockHistory.create(
      {
        userId: Number(winner.userId),
        unlockDate: new Date(),
        totalAmount: REWARDS.WIN,
        availableAmount: REWARDS.WIN * 0.063, // 立即解锁的 6.3% 金额
        withdrawnAmount: 0.0, // 初始提现金额为 0
      },
      { transaction }
    ),

    WalletHistory.create(
      {
        userId: Number(winner.userId),
        walletId: Number(winner.walletId),
        amount: REWARDS.WIN,
        currency: "moof",
        reference: "Payout",
        action: "in",
        category: "moof",
        credit_type: "moof",
        fe_display_remark: "Payout",
        developer_remark: `MOOF 场次: ${getSessionIndex(session_category)}，回合: ${roundIndex}`,
      },
      { transaction }
    ),
    GameHistory.update(
      {
        game_status: "done",
        payout_status: "done",
        payout: REWARDS.WIN,
        is_moof: true,
      },
      { where: { roomId: room.id, walletId: winner.walletId, sessionId: sessionId, round: roundIndex }, transaction }
    ),
  ];
  const winnerResults = await Promise.allSettled(winnerOperations);
  for (const result of winnerResults) {
    if (result.status === "rejected") {
      logger.error(`获胜者操作失败，钱包: ${winner.walletId}，房间: ${room.id}`, result.reason);
      throw new Error(`获胜者操作失败，钱包: ${winner.walletId}`);
    }
  }
  logger.info(`获胜者操作完成，钱包: ${winner.walletId}，房间: ${room.id}`);


  // 处理金牛奖励分享
  await handleBullRewardShare(winner, transaction);
  
  // 处理牛王奖励
  await handleBullKingRewards(winner, transaction);

  // 失败者奖励
  const loserPromises = playerWinRates
    .filter((player) => player.walletId !== winner.walletId)
    .map(async (player) => {
      try {
        const loserOps = [
          UserWallet.increment("usd", { by: REWARDS.FAIL, where: { id: player.walletId }, transaction }),
          Reservation.update(
            { status: "failed" },
            { where: { roomId: room.id, walletId: player.walletId, sessionId, roundIndex }, transaction }
          ),
          GameHistory.update(
            { game_status: "done", payout_status: "done", payout: REWARDS.FAIL },
            { where: { roomId: room.id, walletId: player.walletId, sessionId: sessionId, round: roundIndex }, transaction }
          ),
          WalletHistory.create(
            {
              userId: Number(player.userId),
              walletId: Number(player.walletId),
              amount: REWARDS.FAIL,
              currency: "usd",
              reference: "Payout",
              action: "in",
              category: "usd",
              credit_type: "usd",
              fe_display_remark: "Payout",
              developer_remark: `非 MOOF 场次: ${getSessionIndex(session_category)}，回合: ${roundIndex}`,
            },
            { transaction }
          ),
        ];
        const loserResults = await Promise.allSettled(loserOps);
        for (const res of loserResults) {
          if (res.status === "rejected") {
            logger.error(`失败者操作失败，钱包: ${player.walletId}，房间: ${room.id}`, res.reason);
            throw new Error(`失败者操作失败，钱包: ${player.walletId}`);
          }
        }
        logger.info(`失败者操作完成，钱包: ${player.walletId}，房间: ${room.id}`);
      } catch (error) {
        logger.error(`处理失败者时出错，钱包: ${player.walletId}，房间: ${room.id}:`, error);
        throw error;
      }
    });
  const loserSettled = await Promise.allSettled(loserPromises);
  for (const res of loserSettled) {
    if (res.status === "rejected") {
      throw new Error("一个或多个失败者操作失败");
    }
  }
  
  // 处理所有参与者的门票返利
  // 这部分在事务外执行，不影响主流程
  setTimeout(async () => {
    try {
      // 为每个参与者处理门票返利
      const allPlayers = [...playerWinRates];
      for (const player of allPlayers) {
        try {
          // 每个玩家消耗一张门票，触发返利
          await processTicketRebate(
            player.walletId, 
            1, 
            GAME_CONFIG.TICKET_COST
          );
        } catch (error) {
          logger.error(`处理用户 ${player.userId} 的门票返利时出错:`, error);
        }
      }
    } catch (error) {
      logger.error("处理门票返利时出错:", error);
    }
  }, 1000);
  
  logger.info(`房间 ${room.id} 的奖励发放处理完成`);
};

/**
 * 主任务函数：处理抽奖结果
 */
// 在文件顶部添加导出函数
export async function initializeWorker(queues: { [key: string]: any }) {
  console.log('初始化抽奖结果处理器...');
  // 这里不需要做任何事情，因为 Worker 已经在模块加载时创建
  return lotteryResultWorker;
}
export const lotteryResultJob = async (job: Job) => {
  logger.info(`开始处理任务 ID: ${job.id}，数据:`, job.data);

  // 输入数据校验
  const { roundIndex, session_category } = job.data;
  if (roundIndex === undefined || !session_category) {
    logger.error("无效的任务数据: 缺少 roundIndex 或 session_category");
    throw new Error("无效的任务数据");
  }

  let seqno = 10000;
  // 随机生成一个 seqno（移除了 TON 依赖）
  seqno = Math.floor(Math.random() * 1000000000);
  logger.info(`使用随机生成的 seqno: ${seqno}`);


  const today = dayjs().tz(TZ);
  const sessions = await Session.findAll({
    where: {
      session_dt: {
        [Op.between]: [today.startOf("day").toDate(), today.endOf("day").toDate()],
      },
      session_category,
    },
  });
  if (!sessions.length) {
    logger.warn(`未找到场次，session_category: ${session_category}`);
    return;
  }
  const sessionId = sessions[0].id;
  logger.info(`找到场次: sessionId ${sessionId}`);

  const rooms = await Room.findAll({
    where: { sessionId, roundIndex, lotteryProcessed: false },
  });
  if (!rooms.length) {
    logger.info(`未找到未处理的房间，sessionId: ${sessionId}，roundIndex: ${roundIndex}`);
    return;
  }

  // 针对每个房间独立开启事务处理
  for (const room of rooms) {
    const roomTx = await sequelize.transaction();
    try {
      logger.info(`开始处理房间: roomId ${room.id}`);

      // 从 Redis 获取参与人数
      const roomCountKey = `room:${room.id}:count`;
      const roomCountStr = await redis.get(roomCountKey);
      const roomCount = Number(roomCountStr);
      logger.info(`从 Redis 获取房间 ${room.id} 的参与人数: ${roomCount}`);

      const dbReservationCount = await Reservation.count({
        where: { roomId: room.id, sessionId, roundIndex, status: "reserved" },
        transaction: roomTx,
      });
      if (dbReservationCount !== roomCount) {
        logger.warn(
          `房间 ${room.id} 参与人数不一致: Redis 计数 = ${roomCount}，数据库计数 = ${dbReservationCount}`
        );
      }

      // 如果参与人数为 0
      if (roomCount === 0) {
        logger.info(`房间 ${room.id} 没有参与者，直接标记为已处理`);
        await room.update({ lotteryProcessed: true }, { transaction: roomTx });
        await roomTx.commit();
        // Redis 操作移到事务提交后
        await redis.del(roomCountKey);
        continue;
      }

      // 参与人数不足，执行退款流程
      if (roomCount < REWARDS.MAX_PARTICIPANTS) {
        logger.info(
          `房间 ${room.id} 参与人数不足（<${REWARDS.MAX_PARTICIPANTS}），开始处理退款`
        );
        await handleRefund(room, sessionId, roundIndex, session_category, roomTx);
        await room.update({ lotteryProcessed: true }, { transaction: roomTx });
        await roomTx.commit();
        // Redis 操作移到事务提交后
        await redis.del(roomCountKey);
        continue;
      }

      // ***** 新增 累积各奖金池 *****
      // 计算当前房间的总门票与 USD 消费
      const totalTicketCost = GAME_CONFIG.TICKET_COST * roomCount;
      const totalUsdCost = GAME_CONFIG.USD_COST * roomCount;
      
      // 查询当前房间中使用免费门票的预约数量
      const freeTicketCount = await Reservation.count({
        where: { 
          roomId: room.id, 
          sessionId, 
          roundIndex, 
          status: "reserved",
          ticketType: "free_ticket"
        },
        transaction: roomTx,
      });
      
      // 计算使用普通门票的玩家数量
      const regularTicketCount = roomCount - freeTicketCount;
      
      // 只有普通门票才计入牛王宝座和MOOF持有者奖池
      const regularTicketCost = GAME_CONFIG.TICKET_COST * regularTicketCount;
      const bullKingPoolIncrement = regularTicketCost * 0.1;        // 牛王宝座奖池 10%
      const moofHoldersPoolIncrement = regularTicketCost * 0.3;     // $MOOF 持有者奖金池 30%
      
      // 所有预约（包括免费门票）都计入个人KOL和团队KOL奖池
      const personalKolPoolIncrement = totalUsdCost * 0.015;        // 全球个人 KOL 奖金池 1.5%
      const teamKolPoolIncrement = totalUsdCost * 0.01;             // 全球团队 KOL 奖金池 1%

      // 只有当有普通门票玩家时，才向bull_king和moof_holders奖池添加资金
      if (regularTicketCount > 0) {
        await PrizePool.increment("amount", {
          by: bullKingPoolIncrement,
          where: { type: "bull_king" },
          transaction: roomTx,
        });
        await PrizePool.increment("amount", {
          by: moofHoldersPoolIncrement,
          where: { type: "moof_holders" },
          transaction: roomTx,
        });
      }
      
      // 所有预约都计入个人KOL和团队KOL奖池
      await PrizePool.increment("amount", {
        by: personalKolPoolIncrement,
        where: { type: "personal_kol" },
        transaction: roomTx,
      });
      await PrizePool.increment("amount", {
        by: teamKolPoolIncrement,
        where: { type: "team_kol" },
        transaction: roomTx,
      });
      
      logger.info(
        `房间 ${room.id} 累积奖池金额：牛王 ${regularTicketCount > 0 ? bullKingPoolIncrement : 0}，$MOOF ${regularTicketCount > 0 ? moofHoldersPoolIncrement : 0}，个人 KOL ${personalKolPoolIncrement}，团队 KOL ${teamKolPoolIncrement}，免费门票数 ${freeTicketCount}`
      );
      // **************************************************

      // 参与人数足够，执行抽奖流程
      const reservations = await Reservation.findAll({
        where: { roomId: room.id, sessionId, roundIndex, status: "reserved" },
        order: [["id", "ASC"]], // 按预约时间升序排序
        transaction: roomTx,
      });
      if (!reservations.length) {
        logger.info(`房间 ${room.id} 没有有效的预约记录，跳过抽奖`);
        await room.update({ lotteryProcessed: true }, { transaction: roomTx });
        await redis.del(roomCountKey);
        await roomTx.commit();
        continue;
      }

      // 为每个玩家分配座位号
      let playerWinRates = reservations.map((r, index) => ({
        walletId: r.walletId,
        userId: r.userId,
        player_index: index + 1, // 座位号从1开始
      }));

      // 计算获胜座位号
      const roomPlayerCount = playerWinRates.length;
      const winnerSeat = (seqno % roomPlayerCount) + 1;
      logger.info(`房间 ${room.id} 的获胜座位号为: ${winnerSeat}`);

      // 检查是否存在手动指定的获胜者
      if (room.manualWinnerWalletId) {
        const manualWinnerIndex = playerWinRates.findIndex((player) => player.walletId === room.manualWinnerWalletId);
        if (manualWinnerIndex !== -1) {
          const manualWinner = playerWinRates[manualWinnerIndex];
          const currentSeat = manualWinner.player_index;

          if (currentSeat !== winnerSeat) {
            // 找到获胜座位号的玩家
            const currentWinnerSeatPlayerIndex = playerWinRates.findIndex((player) => player.player_index === winnerSeat);
            if (currentWinnerSeatPlayerIndex !== -1) {
              // 调换座位号
              const tempIndex = playerWinRates[currentWinnerSeatPlayerIndex].player_index;
              playerWinRates[currentWinnerSeatPlayerIndex].player_index = manualWinner.player_index;
              manualWinner.player_index = tempIndex;
              logger.info(
                `调整座位号: 将手动获胜者 ${manualWinner.walletId} 设置为座位 ${winnerSeat}，原座位 ${winnerSeat} 的玩家 ${playerWinRates[currentWinnerSeatPlayerIndex].walletId} 移到座位 ${currentSeat}`
              );
            }
          } else {
            logger.info(`手动获胜者 ${manualWinner.walletId} 已在获胜座位 ${winnerSeat}，无需调整`);
          }
        } else {
          logger.warn(`手动指定的获胜者钱包 ID ${room.manualWinnerWalletId} 未在房间 ${room.id} 的参与者中找到，切换到随机模式`);
        }
      }

      // 找到获胜者
      const winner = playerWinRates.find((player) => player.player_index === winnerSeat);
      if (!winner) {
        logger.error(`未找到座位号 ${winnerSeat} 的获胜者，房间: ${room.id}`);
        throw new Error("获胜者未找到");
      }
      logger.info(`房间 ${room.id} 获胜者为用户: ${winner.walletId}，座位号: ${winnerSeat}`);

      // 记录 seqno 和 result_position 到 Room 表
      await room.update(
        {
          seqno: seqno,
          result_position: winner.player_index,
          lotteryProcessed: true,
        },
        { transaction: roomTx }
      );

      // 处理奖励
      await handleRewards(room, winner, playerWinRates, sessionId, roundIndex, session_category, roomTx);
      
      logger.info(`房间 ${room.id} 的奖励已处理`);
      await roomTx.commit();
      // Redis 操作移到事务提交后
      await redis.del(roomCountKey);
    } catch (error) {
      logger.error(`处理房间 ${room.id} 时出错:`, error);
      await roomTx.rollback();
    }
  }
  logger.info(`任务 ID: ${job.id} 已完成，处理了 ${rooms.length} 个房间。`);
};

// Worker 配置保持不变
logger.info("启动 Lottery Worker...");

const lotteryResultWorker = new Worker("lottery-result-job", lotteryResultJob, {
  connection: redis,
  autorun: true,
  concurrency: 1,
  removeOnComplete: { count: 1000 },
  removeOnFail: { count: 1000 },
});

lotteryResultWorker.on("completed", (job: Job) => {
  logger.info(`任务 ${job.id} 已完成。`);
});

lotteryResultWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

lotteryResultWorker.on("error", (error: Error) => {
  logger.error("Worker 出错:", error);
});

lotteryResultWorker.on("active", (job: Job) => {
  logger.info(`Worker 接收到新任务: ID=${job.id}，数据:`, job.data);
});

lotteryResultWorker.on("stalled", (jobId: string) => {
  logger.warn(`任务 ${jobId} 已停滞。`);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('抽奖工作进程收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await lotteryResultWorker.close();
      
      logger.info('抽奖工作进程资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('抽奖工作进程清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on("SIGTERM", async () => {
  logger.info("收到 SIGTERM 信号，关闭 Worker...");
  try {
    await lotteryResultWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 监听 SIGINT 信号
process.on("SIGINT", async () => {
  logger.info("收到 SIGINT 信号，关闭 Worker...");
  try {
    await lotteryResultWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 修改现有的异常处理
process.on("uncaughtException", async (error) => {
  logger.error("未捕获的异常:", error);
  try {
    await lotteryResultWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

process.on("unhandledRejection", async (reason) => {
  logger.error("未处理的拒绝:", reason);
  try {
    await lotteryResultWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

logger.info("Lottery Worker 初始化完成，准备处理任务。");