import { Injectable, Inject, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Queue, Worker, Job } from 'bullmq';
import { Redis } from 'ioredis';

@Injectable()
export class QueueService implements OnModuleInit, OnModuleDestroy {
  private queues: Map<string, Queue> = new Map();
  private workers: Map<string, Worker> = new Map();

  constructor(@Inject('REDIS_CLIENT') private readonly redisClient: Redis) {}

  async onModuleInit() {
    console.log('[QueueService] 初始化队列服务...');
    await this.initializeQueues();
  }

  async onModuleDestroy() {
    console.log('[QueueService] 关闭队列服务...');
    await this.closeAllQueues();
    await this.closeAllWorkers();
  }

  private async initializeQueues() {
    // 创建各种队列
    const queueConfigs = [
      'lottery-result-job',
      'moof-holders-reward-job',
      'personal-kol-reward-job',
      'team-kol-reward-job',
      'daily-rebate-settlement-job',
      'jackpot-chest-queue',
      'withdrawal-queue',
      'kaia-price-update-job',
    ];

    for (const queueName of queueConfigs) {
      const queue = new Queue(queueName, {
        connection: {
          host: this.redisClient.options.host,
          port: this.redisClient.options.port,
          password: this.redisClient.options.password,
          db: this.redisClient.options.db,
        },
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      this.queues.set(queueName, queue);
      console.log(`[QueueService] 队列 ${queueName} 已创建`);
    }
  }

  getQueue(queueName: string): Queue | undefined {
    return this.queues.get(queueName);
  }

  async addJob(queueName: string, jobName: string, data: any, options?: any): Promise<Job | null> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      console.error(`[QueueService] 队列 ${queueName} 不存在`);
      return null;
    }

    try {
      const job = await queue.add(jobName, data, options);
      console.log(`[QueueService] 任务已添加到队列 ${queueName}: ${jobName}`);
      return job;
    } catch (error) {
      console.error(`[QueueService] 添加任务失败 ${queueName}:${jobName}`, error);
      return null;
    }
  }

  async addWorker(queueName: string, processor: (job: Job) => Promise<any>, options?: any): Promise<Worker | null> {
    if (this.workers.has(queueName)) {
      console.warn(`[QueueService] Worker ${queueName} 已存在`);
      return this.workers.get(queueName) || null;
    }

    try {
      const worker = new Worker(queueName, processor, {
        connection: this.redisClient,
        concurrency: 1,
        ...options,
      });

      worker.on('completed', (job) => {
        console.log(`[QueueService] 任务完成: ${queueName}:${job?.name}`);
      });

      worker.on('failed', (job, error) => {
        console.error(`[QueueService] 任务失败: ${queueName}:${job?.name}`, error);
      });

      worker.on('error', (error) => {
        console.error(`[QueueService] Worker错误 ${queueName}:`, error);
      });

      this.workers.set(queueName, worker);
      console.log(`[QueueService] Worker ${queueName} 已创建`);
      return worker;
    } catch (error) {
      console.error(`[QueueService] 创建Worker失败 ${queueName}:`, error);
      return null;
    }
  }

  async removeJob(queueName: string, jobId: string): Promise<boolean> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      return false;
    }

    try {
      const job = await queue.getJob(jobId);
      if (job) {
        await job.remove();
        return true;
      }
      return false;
    } catch (error) {
      console.error(`[QueueService] 移除任务失败 ${queueName}:${jobId}`, error);
      return false;
    }
  }

  async getJobCounts(queueName: string): Promise<any> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      return null;
    }

    try {
      return await queue.getJobCounts();
    } catch (error) {
      console.error(`[QueueService] 获取任务统计失败 ${queueName}:`, error);
      return null;
    }
  }

  private async closeAllQueues() {
    for (const [name, queue] of this.queues) {
      try {
        await queue.close();
        console.log(`[QueueService] 队列 ${name} 已关闭`);
      } catch (error) {
        console.error(`[QueueService] 关闭队列失败 ${name}:`, error);
      }
    }
    this.queues.clear();
  }

  private async closeAllWorkers() {
    for (const [name, worker] of this.workers) {
      try {
        await worker.close();
        console.log(`[QueueService] Worker ${name} 已关闭`);
      } catch (error) {
        console.error(`[QueueService] 关闭Worker失败 ${name}:`, error);
      }
    }
    this.workers.clear();
  }

  // 获取所有队列的状态
  async getAllQueuesStatus(): Promise<any> {
    const status: any = {};
    for (const [name, queue] of this.queues) {
      try {
        status[name] = await queue.getJobCounts();
      } catch (error) {
        status[name] = { error: error.message };
      }
    }
    return status;
  }
}
