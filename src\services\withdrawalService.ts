import { sequelize } from "../config/db";
import { UserWallet, WalletHistory, Withdrawal } from "../models";
import { Transaction,Op } from "sequelize";
import { t } from "../i18n";
import BigNumber from 'bignumber.js';
import { createBigNumber } from '../utils/bigNumberConfig';

// 提现设置常量
export const WITHDRAWAL_SETTINGS = {
  USD: {
    minAmount: 10, // 最小出金金额
    fee: 1, // 平台费
    dailyLimit: 3, // 每日最多出金次数
    manualApproveThreshold: 1000 // 需要手动审核的阈值
  },
  MOOF: {
    minAmount: 21, // 最小出金金额
    fee: 10, // 平台费
    dailyLimit: 3 // 每日最多出金次数
  },

};

/**
 * 获取提现设置列表
 * @returns 提现设置列表
 */
export async function getWithdrawalSettings() {
  return {
    usd: {
      minAmount: WITHDRAWAL_SETTINGS.USD.minAmount,
      fee: WITHDRAWAL_SETTINGS.USD.fee,
      dailyLimit: WITHDRAWAL_SETTINGS.USD.dailyLimit,
      manualApproveThreshold: WITHDRAWAL_SETTINGS.USD.manualApproveThreshold
    },
    moof: {
      minAmount: WITHDRAWAL_SETTINGS.MOOF.minAmount,
      fee: WITHDRAWAL_SETTINGS.MOOF.fee,
      dailyLimit: WITHDRAWAL_SETTINGS.MOOF.dailyLimit
    }
  };
}

/**
 * 检查用户今日提现次数
 * @param userId 用户ID
 * @param currency 货币类型
 * @returns 今日提现次数
 */
async function getTodayWithdrawalCount(userId: number, currency: string) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const count = await WalletHistory.count({
    where: {
      userId,
      currency: currency.toLowerCase(),
      action: 'out',
      category: 'withdrawal',
      createdAt: {
        [Op.gte]: today
      }
    }
  });
  
  return count;
}

/**
 * 提现USD
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param amount 提现金额
 * @param address 提现地址
 * @returns 提现结果
 */
export async function withdrawUSD(userId: number, walletId: number, amount: number, address: string) {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 检查提现金额是否满足最低要求
    if (amount < WITHDRAWAL_SETTINGS.USD.minAmount) {
      throw new Error(t("errors.withdrawalMinAmount", { minAmount: WITHDRAWAL_SETTINGS.USD.minAmount, currency: "USD" }));
    }
    
    // 2. 检查今日提现次数
    const todayCount = await getTodayWithdrawalCount(userId, 'usd');
    if (todayCount >= WITHDRAWAL_SETTINGS.USD.dailyLimit) {
      throw new Error(t("errors.withdrawalDailyLimitReached", { dailyLimit: WITHDRAWAL_SETTINGS.USD.dailyLimit }));
    }
    
    // 3. 获取用户钱包
    const wallet = await UserWallet.findOne({
      where: { id: walletId, userId },
      transaction,
      lock: true
    });
    
    if (!wallet) {
      throw new Error(t("errors.userWalletNotFound"));
    }
    
    // 4. 检查余额是否足够
    const totalAmount = new BigNumber(amount).plus(WITHDRAWAL_SETTINGS.USD.fee).toNumber();
    if (!wallet.usd || wallet.usd < totalAmount) {
      throw new Error(t("errors.insufficientFundsWithFee", { totalAmount, fee: WITHDRAWAL_SETTINGS.USD.fee, currency: "USD" }));
    }
    
    // 5. 扣除用户余额
    await wallet.decrement('usd', { by: totalAmount, transaction });
    
    // 6. 确定审核状态
    const needManualApprove = amount >= WITHDRAWAL_SETTINGS.USD.manualApproveThreshold;
    const status = needManualApprove ? 'pending' : 'approved';
    
    // 7. 记录提现历史到WalletHistory
    await WalletHistory.create({
      userId,
      walletId,
      amount: amount,
      fee: WITHDRAWAL_SETTINGS.USD.fee,
      currency: 'usd',
      reference: `Withdrawal`,
      action: 'out',
      category: 'withdrawal',
      credit_type: 'usd',
      status,
      withdrawalAddress: address,
      fe_display_remark: t("walletHistory.withdrawalUSD", { address }),
      developer_remark: t("walletHistory.withdrawalUSDDev", { amount, fee: WITHDRAWAL_SETTINGS.USD.fee, status }),
    }, { transaction });
    
    // 8. 记录到专门的提现表
    await Withdrawal.create({
      userId,
      walletId,
      amount: amount,
      fee: WITHDRAWAL_SETTINGS.USD.fee,
      currency: 'usd',
      status,
      withdrawalAddress: address,
      remark: t("walletHistory.withdrawalUSDDev", { amount, fee: WITHDRAWAL_SETTINGS.USD.fee, status }),
      // 初始化区块链交易信息字段
      txStatus: 'pending', // 交易状态初始为pending
      // 其他区块链字段会在交易发送后更新
    }, { transaction });
    
    await transaction.commit();
    
    return {
      success: true,
      amount,
      fee: WITHDRAWAL_SETTINGS.USD.fee,
      status,
      needManualApprove
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

/**
 * 提现MOOF
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param amount 提现金额
 * @param address 提现地址
 * @returns 提现结果
 */
export async function withdrawMOOF(userId: number, walletId: number, amount: number, address: string) {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 检查提现金额是否满足最低要求
    if (amount < WITHDRAWAL_SETTINGS.MOOF.minAmount) {
      throw new Error(t("errors.withdrawalMinAmount", { minAmount: WITHDRAWAL_SETTINGS.MOOF.minAmount, currency: "MOOF" }));
    }
    
    // 2. 检查今日提现次数
    const todayCount = await getTodayWithdrawalCount(userId, 'moof');
    if (todayCount >= WITHDRAWAL_SETTINGS.MOOF.dailyLimit) {
      throw new Error(t("errors.withdrawalDailyLimitReached", { dailyLimit: WITHDRAWAL_SETTINGS.MOOF.dailyLimit }));
    }
    
    // 3. 获取用户钱包
    const wallet = await UserWallet.findOne({
      where: { id: walletId, userId },
      transaction,
      lock: true
    });
    
    if (!wallet) {
      throw new Error(t("errors.userWalletNotFound"));
    }
    
    // 4. 检查解锁的MOOF余额是否足够
    const totalAmount = new BigNumber(amount).plus(WITHDRAWAL_SETTINGS.MOOF.fee).toNumber();
    const unlockMoof = wallet.unlockMoof || 0;
    
    if (unlockMoof < totalAmount) {
      throw new Error(t("errors.insufficientUnlockedMOOF", { totalAmount, fee: WITHDRAWAL_SETTINGS.MOOF.fee }));
    }
    
    // 5. 扣除用户解锁的MOOF余额
    await wallet.decrement('unlockMoof', { by: totalAmount, transaction });
    
    // 6. 记录提现历史到WalletHistory
    await WalletHistory.create({
      userId,
      walletId,
      amount: amount,
      fee: WITHDRAWAL_SETTINGS.MOOF.fee,
      currency: 'moof',
      reference: `Withdrawal MOOF`,
      action: 'out',
      category: 'withdrawal',
      credit_type: 'moof',
      status: 'approved', // MOOF提现默认自动审核通过
      withdrawalAddress: address,
      fe_display_remark: t("walletHistory.withdrawalMOOF", { address }),
      developer_remark: t("walletHistory.withdrawalMOOFDev", { amount, fee: WITHDRAWAL_SETTINGS.MOOF.fee }),
    }, { transaction });
    
    // 7. 记录到专门的提现表
    await Withdrawal.create({
      userId,
      walletId,
      amount: amount,
      fee: WITHDRAWAL_SETTINGS.MOOF.fee,
      currency: 'moof',
      status: 'approved', // MOOF提现默认自动审核通过
      withdrawalAddress: address,
      remark: t("walletHistory.withdrawalMOOFDev", { amount, fee: WITHDRAWAL_SETTINGS.MOOF.fee }),
      // 初始化区块链交易信息字段
      txStatus: 'pending', // 交易状态初始为pending
      // 其他区块链字段会在交易发送后更新
    }, { transaction });
    
    await transaction.commit();
    
    return {
      success: true,
      amount,
      fee: WITHDRAWAL_SETTINGS.MOOF.fee,
      status: 'approved'
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}



/**
 * 获取用户MOOF相关数量
 * @param walletId 钱包ID
 * @returns MOOF相关数量
 */
export async function getMoofBalances(walletId: number) {
  const wallet = await UserWallet.findOne({
    where: { id: walletId }
  });
  
  if (!wallet) {
    throw new Error(t("errors.userWalletNotFound"));
  }
  
  return {
    totalMoof: wallet.moof || 0, // 总计MOOF
    lockedMoof: (wallet.moof || 0) - (wallet.unlockMoof || 0), // 锁仓总计MOOF
    unlockedMoof: wallet.unlockMoof || 0 // 解锁MOOF数量
  };
}