import { ValidationPipe, BadRequestException } from '@nestjs/common';
import { ValidationError } from 'class-validator';
import { CustomI18nService } from '../../i18n/i18n.service';

export class I18nValidationPipe extends ValidationPipe {
  constructor(private readonly i18nService: CustomI18nService) {
    super({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      exceptionFactory: (errors: ValidationError[]) => {
        return this.createI18nException(errors);
      },
    });
  }

  private createI18nException(errors: ValidationError[]): BadRequestException {
    const formattedErrors = this.formatErrors(errors);
    
    return new BadRequestException({
      message: 'Validation failed',
      errors: formattedErrors,
    });
  }

  private formatErrors(errors: ValidationError[], parentPath = ''): any[] {
    const result = [];

    for (const error of errors) {
      const propertyPath = parentPath ? `${parentPath}.${error.property}` : error.property;

      if (error.constraints) {
        for (const [constraintKey, constraintValue] of Object.entries(error.constraints)) {
          // 尝试翻译验证消息
          const translationKey = `validation.${constraintKey}`;
          let translatedMessage = this.i18nService.translate(translationKey);
          
          // 如果翻译失败，使用原始消息
          if (translatedMessage === translationKey) {
            translatedMessage = constraintValue;
          }

          result.push({
            property: propertyPath,
            constraint: constraintKey,
            message: translatedMessage,
            value: error.value,
          });
        }
      }

      // 处理嵌套验证错误
      if (error.children && error.children.length > 0) {
        result.push(...this.formatErrors(error.children, propertyPath));
      }
    }

    return result;
  }
}
