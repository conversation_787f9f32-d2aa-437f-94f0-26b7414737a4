import * as Jo<PERSON> from 'joi';

// 环境变量验证模式
export const validationSchema = Joi.object({
  // 应用配置
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  NESTJS_PORT: Joi.number().default(3001),
  
  // JWT 配置
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('24h'),
  
  // 数据库配置
  DB_HOST: Joi.string().default('localhost'),
  DB_PORT: Joi.number().default(3306),
  DB_USER: Joi.string().required(),
  DB_PASSWORD: Joi.string().allow('').default(''),
  DB_PASS: Joi.string().allow('').default(''),
  DB_NAME: Joi.string().required(),
  
  // Redis 配置
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().allow('').default(''),
  REDIS_PASS: Joi.string().allow('').default(''),
  REDIS_DB: Joi.number().default(0),
  
  // 日志配置
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('debug'),
  
  // CORS 配置
  CORS_ORIGIN: Joi.string().default('*'),
});
