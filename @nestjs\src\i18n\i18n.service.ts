import { Injectable } from '@nestjs/common';
import { I18nService, I18nContext } from 'nestjs-i18n';

// 支持的语言类型
export type SupportedLanguage = 'en' | 'zh' | 'zh-tw' | 'ja';

// 支持的语言列表
export const SUPPORTED_LANGUAGES: SupportedLanguage[] = ['en', 'zh', 'zh-tw', 'ja'];

@Injectable()
export class CustomI18nService {
  constructor(private readonly i18nService: I18nService) {}

  /**
   * 翻译文本
   * @param key 翻译键
   * @param options 插值选项
   * @param lang 指定语言
   * @returns 翻译后的文本
   */
  translate(
    key: string, 
    options?: Record<string, any>, 
    lang?: SupportedLanguage
  ): string {
    try {
      if (lang) {
        return this.i18nService.translate(key, {
          lang,
          args: options,
        });
      }
      
      // 尝试从当前上下文获取语言
      const currentLang = I18nContext.current()?.lang || 'en';
      return this.i18nService.translate(key, {
        lang: currentLang,
        args: options,
      });
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error);
      return key; // 返回原始键作为后备
    }
  }

  /**
   * 兼容原有的 t 函数
   * @param key 翻译键
   * @param options 插值选项
   * @param lang 指定语言
   * @returns 翻译后的文本
   */
  t(
    key: string, 
    options?: Record<string, any>, 
    lang?: SupportedLanguage
  ): string {
    return this.translate(key, options, lang);
  }

  /**
   * 从请求对象获取翻译（兼容原有的 tFromRequest 函数）
   * @param req 请求对象
   * @param key 翻译键
   * @param options 插值选项
   * @returns 翻译后的文本
   */
  tFromRequest(
    req: any, 
    key: string, 
    options?: Record<string, any>
  ): string {
    const language = req?.language || req?.i18nLang || 'en';
    return this.translate(key, options, language as SupportedLanguage);
  }

  /**
   * 查找最匹配的语言
   * @param lang 语言代码
   * @returns 匹配的语言
   */
  findBestMatchingLanguage(lang: string): SupportedLanguage {
    if (!lang) return 'en';
    
    // 精确匹配
    const exactMatch = SUPPORTED_LANGUAGES.find(item => item === lang.toLowerCase());
    if (exactMatch) return exactMatch;
    
    // 部分匹配（如 zh-CN 匹配 zh）
    const partialMatch = SUPPORTED_LANGUAGES.find(item => 
      item.startsWith(lang.toLowerCase()) || lang.toLowerCase().startsWith(item)
    );
    
    return partialMatch || 'en';
  }

  /**
   * 获取当前语言
   * @returns 当前语言
   */
  getCurrentLanguage(): SupportedLanguage {
    try {
      const currentLang = I18nContext.current()?.lang;
      return this.findBestMatchingLanguage(currentLang || 'en');
    } catch {
      return 'en';
    }
  }

  /**
   * 格式化验证错误（兼容原有系统）
   * @param errors 错误数组
   * @param lang 语言
   * @returns 格式化后的错误
   */
  formatValidationErrors(
    errors: any[], 
    lang: SupportedLanguage = 'en'
  ): Array<{ field: string; message: string }> {
    if (!errors || !Array.isArray(errors)) return [];
    
    return errors.map(error => ({
      field: error.instancePath || error.property || '',
      message: this.translate(`validation.${error.keyword}`, error, lang) || error.message || 'Validation error'
    }));
  }

  /**
   * 处理自定义错误消息（兼容原有系统）
   * @param errorMessage 错误消息
   * @param lang 语言
   * @returns 翻译后的错误消息
   */
  processErrorMessage(errorMessage: string, lang: SupportedLanguage = 'en'): string {
    // 处理宝箱数量无效错误
    if (errorMessage === 'CHEST_OPEN_COUNT_INVALID') {
      return this.translate('errors.chestOpenCountInvalid', { count: 0 }, lang);
    }
    
    // 处理宝箱数量不足错误
    if (errorMessage.startsWith('NOT_ENOUGH_CHESTS:')) {
      const parts = errorMessage.split(':');
      if (parts.length === 3) {
        const required = Number(parts[1]);
        const available = Number(parts[2]);
        return this.translate('errors.notEnoughChests', { required, available }, lang);
      }
    }
    
    // 尝试翻译错误键
    const translatedError = this.translate(`errors.${errorMessage}`, {}, lang);
    return translatedError !== `errors.${errorMessage}` ? translatedError : errorMessage;
  }

  /**
   * 手动替换变量（兼容原有系统）
   * @param text 原始文本
   * @param variables 变量对象
   * @returns 替换后的文本
   */
  manualReplaceVariables(text: string, variables: Record<string, any>): string {
    let result = text;
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      result = result.replace(new RegExp(`{${key}}`, 'g'), String(value));
    });
    return result;
  }

  /**
   * 获取并替换翻译文本中的变量（兼容原有系统）
   * @param key 翻译键
   * @param variables 变量对象
   * @param lang 语言代码
   * @returns 替换后的文本
   */
  tWithVariables(
    key: string, 
    variables: Record<string, any>, 
    lang: SupportedLanguage = 'en'
  ): string {
    const template = this.translate(key, {}, lang);
    return this.manualReplaceVariables(template, variables);
  }
}
