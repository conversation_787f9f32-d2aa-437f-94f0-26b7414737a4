import { Is<PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TestValidationDto {
  @ApiProperty({ description: 'User name', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Optional message', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  message?: string;
}
