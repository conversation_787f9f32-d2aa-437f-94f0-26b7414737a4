// /src/routes/reset-user-reservations.ts
import { Router, Request, Response } from "express";
import { UserWallet } from "../models/UserWallet";
import { sequelize } from "../config/db";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);
// 定义添加测试币请求体验证模式
const addTestCoinSchema = {
  type: "object",
  properties: {
    fields: { 
      type: "object",
      additionalProperties: {
        type: "number",
        minimum: 0.000001
      }
    },
    targetAll: { type: "boolean" }
  },
  required: ["fields"]
};

const validateAddTestCoin = ajv.compile(addTestCoinSchema);

//@ts-ignore
router.post("/add-test-coin", walletAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const myReq = req as MyRequest;
    const { userId } = myReq.user || {};
    const { fields, targetAll = false } = req.body;
    
    // 验证请求体
    const valid = validateAddTestCoin(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateAddTestCoin.errors || [], req.language)
      ));
    }
    
    // 验证fields是否为对象
    if (!fields || typeof fields !== 'object' || Object.keys(fields).length === 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidFields")
      ));
    }

    // 验证字段是否有效
    const validFields = ["ton", "gem", "ticket", "usd", "moof", "unlockMoof","fragment_green","fragment_blue","fragment_purple","fragment_gold"];
    const updateData: any = {};
    const invalidFields: string[] = [];
    const invalidValues: string[] = [];

    // 检查每个字段和值是否有效
    for (const [field, amount] of Object.entries(fields)) {
      if (!validFields.includes(field)) {
        invalidFields.push(field);
        continue;
      }
      
      if (typeof amount !== "number" || amount <= 0) {
        invalidValues.push(field);
        continue;
      }
      
      updateData[field] = sequelize.literal(`${field} + ${amount}`);
    }

    // 如果有无效字段或值，返回错误
    if (invalidFields.length > 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidFieldNames", { 
          fields: invalidFields.join(", "), 
          validFields: validFields.join(", ") 
        })
      ));
    }

    if (invalidValues.length > 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidFieldValues", { 
          fields: invalidValues.join(", ") 
        })
      ));
    }

    // 如果没有有效的更新字段，返回错误
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.noValidFieldsToUpdate")
      ));
    }

    // 构建查询条件
    const whereCondition: any = {};
    if (!targetAll) {
      // 如果不是更新所有账户，则只更新当前用户的账户
      if (!userId) {
        return res.status(401).json(errorResponse(
          tFromRequest(req, "errors.unauthorized")
        ));
      }
      whereCondition.userId = userId;
    }

    // 执行更新
    const [updatedCount] = await UserWallet.update(updateData, { where: whereCondition });

    // 构建成功消息
    const updatedFields = Object.entries(fields)
      .filter(([field]) => validFields.includes(field))
      .map(([field, amount]) => `${field}: +${amount}`)
      .join(", ");

    return res.status(200).json(successResponse(
      { updatedCount },
      targetAll 
        ? tFromRequest(req, "success.addTestCoinAllAccounts", { fields: updatedFields }) 
        : tFromRequest(req, "success.addTestCoinYourAccount", { fields: updatedFields })
    ));
  } catch (error) {
    console.error("更新用户钱包出错：", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"),
      (error as Error).message
    ));
  }
});

export default router;
