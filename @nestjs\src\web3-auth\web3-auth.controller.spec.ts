import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Web3AuthController } from './web3-auth.controller';
import { Web3AuthService } from './web3-auth.service';
import { TranslationService } from '../common/services/translation.service';
import { Web3AuthValidationPipe } from './pipes/web3-auth-validation.pipe';

describe('Web3AuthController', () => {
  let controller: Web3AuthController;
  let mockWeb3AuthService: any;
  let mockTranslationService: any;

  beforeEach(async () => {
    // 创建服务模拟
    mockWeb3AuthService = {
      checkRedisConnection: jest.fn(),
      getNonce: jest.fn(),
    };

    mockTranslationService = {
      tFromRequest: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [Web3AuthController],
      providers: [
        {
          provide: Web3AuthService,
          useValue: mockWeb3AuthService,
        },
        {
          provide: TranslationService,
          useValue: mockTranslationService,
        },
        {
          provide: Web3AuthValidationPipe,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<Web3AuthController>(Web3AuthController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('healthCheck', () => {
    it('should return healthy status when Redis is connected', async () => {
      mockWeb3AuthService.checkRedisConnection.mockResolvedValue(true);

      const result = await controller.healthCheck();

      expect(result).toEqual({
        ok: true,
        data: {
          redis: 'connected',
          timestamp: expect.any(String),
        },
      });
    });

    it('should return unhealthy status when Redis is disconnected', async () => {
      mockWeb3AuthService.checkRedisConnection.mockResolvedValue(false);

      const result = await controller.healthCheck();

      expect(result).toEqual({
        ok: true,
        data: {
          redis: 'disconnected',
          timestamp: expect.any(String),
        },
      });
    });

    it('should throw HttpException when health check fails', async () => {
      mockWeb3AuthService.checkRedisConnection.mockRejectedValue(
        new Error('Health check error')
      );

      await expect(controller.healthCheck()).rejects.toThrow(HttpException);
    });
  });

  describe('getNonce', () => {
    const mockRequest = {
      language: 'en',
      headers: { 'accept-language': 'en' },
    } as any;

    it('should return nonce successfully', async () => {
      const mockNonceData = {
        nonce: 'test-nonce-123',
        message: 'Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: test-nonce-123\nTimestamp: **********',
      };

      mockWeb3AuthService.getNonce.mockResolvedValue(mockNonceData);

      const result = await controller.getNonce(
        { walletAddress: '0x**********abcdef**********abcdef12345678' },
        mockRequest
      );

      expect(result).toEqual({
        ok: true,
        data: mockNonceData,
      });
    });

    it('should handle invalid wallet address error', async () => {
      mockWeb3AuthService.getNonce.mockRejectedValue(
        new Error('Invalid wallet address format')
      );
      mockTranslationService.tFromRequest.mockReturnValue('Invalid address');

      await expect(
        controller.getNonce(
          { walletAddress: '' },
          mockRequest
        )
      ).rejects.toThrow(
        expect.objectContaining({
          message: expect.objectContaining({
            ok: false,
            message: 'Invalid address',
            error: 'Invalid wallet address format',
          }),
          status: HttpStatus.BAD_REQUEST,
        })
      );
    });

    it('should handle Redis storage error', async () => {
      mockWeb3AuthService.getNonce.mockRejectedValue(
        new Error('Failed to store nonce in Redis')
      );
      mockTranslationService.tFromRequest.mockReturnValue('Server error');

      await expect(
        controller.getNonce(
          { walletAddress: '0x**********abcdef**********abcdef12345678' },
          mockRequest
        )
      ).rejects.toThrow(
        expect.objectContaining({
          message: expect.objectContaining({
            ok: false,
            message: 'Server error',
            error: 'Failed to store nonce in Redis',
          }),
          status: HttpStatus.INTERNAL_SERVER_ERROR,
        })
      );
    });

    it('should handle generic server error', async () => {
      mockWeb3AuthService.getNonce.mockRejectedValue(
        new Error('Unknown error')
      );
      mockTranslationService.tFromRequest.mockReturnValue('Server error');

      await expect(
        controller.getNonce(
          { walletAddress: '0x**********abcdef**********abcdef12345678' },
          mockRequest
        )
      ).rejects.toThrow(
        expect.objectContaining({
          message: expect.objectContaining({
            ok: false,
            message: 'Server error',
            error: 'Unknown error',
          }),
          status: HttpStatus.INTERNAL_SERVER_ERROR,
        })
      );
    });

    it('should call translation service with correct parameters', async () => {
      const mockNonceData = {
        nonce: 'test-nonce',
        message: 'test message',
      };

      mockWeb3AuthService.getNonce.mockResolvedValue(mockNonceData);

      await controller.getNonce(
        { walletAddress: '0x**********abcdef**********abcdef12345678' },
        mockRequest
      );

      // 验证没有调用翻译服务（因为没有错误）
      expect(mockTranslationService.tFromRequest).not.toHaveBeenCalled();
    });

    it('should handle different error types with appropriate status codes', async () => {
      const testCases = [
        {
          error: 'Invalid wallet address format',
          expectedStatus: HttpStatus.BAD_REQUEST,
          translationKey: 'errors.invalidAddress',
        },
        {
          error: 'Failed to store nonce in Redis',
          expectedStatus: HttpStatus.INTERNAL_SERVER_ERROR,
          translationKey: 'errors.serverError',
        },
        {
          error: 'Some other error',
          expectedStatus: HttpStatus.INTERNAL_SERVER_ERROR,
          translationKey: 'errors.serverError',
        },
      ];

      for (const testCase of testCases) {
        mockWeb3AuthService.getNonce.mockRejectedValue(
          new Error(testCase.error)
        );
        mockTranslationService.tFromRequest.mockReturnValue('Translated error');

        await expect(
          controller.getNonce(
            { walletAddress: '0x**********abcdef**********abcdef12345678' },
            mockRequest
          )
        ).rejects.toThrow(
          expect.objectContaining({
            status: testCase.expectedStatus,
          })
        );

        expect(mockTranslationService.tFromRequest).toHaveBeenCalledWith(
          mockRequest,
          testCase.translationKey
        );

        // 重置模拟
        mockTranslationService.tFromRequest.mockClear();
      }
    });
  });
});
