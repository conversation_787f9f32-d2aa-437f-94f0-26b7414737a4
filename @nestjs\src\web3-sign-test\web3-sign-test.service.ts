import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ethers } from 'ethers';
import { SignMessageDto, SignMessageResponseDto } from './dto/sign-message.dto';

@Injectable()
export class Web3SignTestService {
  private readonly logger = new Logger(Web3SignTestService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 使用服务器私钥签名消息
   * @param signMessageDto 签名请求参数
   * @returns 签名结果
   */
  async signMessage(signMessageDto: SignMessageDto): Promise<SignMessageResponseDto> {
    const { message } = signMessageDto;
    const startTime = Date.now();

    this.logger.debug(`开始签名消息，消息长度: ${message.length}`);

    try {
      // 从环境变量获取私钥（测试环境使用）
      const privateKey = this.configService.get<string>('TEST_ETH_PRIVATE_KEY');
      
      if (!privateKey) {
        this.logger.error('TEST_ETH_PRIVATE_KEY 环境变量未设置');
        throw new Error('TEST_ETH_PRIVATE_KEY not set in environment variables');
      }

      // 使用私钥创建钱包实例
      const wallet = new ethers.Wallet(privateKey);
      
      // 签名消息
      const signature = await wallet.signMessage(message);
      
      const duration = Date.now() - startTime;
      this.logger.debug(`消息签名完成，钱包地址: ${wallet.address}, 耗时: ${duration}ms`);
      
      return {
        signature,
        address: wallet.address,
        message
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`签名消息失败，耗时: ${duration}ms`, error);
      
      // 重新抛出错误，让控制器处理
      throw error;
    }
  }
}
