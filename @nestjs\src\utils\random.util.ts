import * as crypto from 'crypto';

/**
 * 生成唯一的随机代码
 * @param length 代码长度
 * @returns 随机代码
 */
export function generateUniqueCode(length: number): string {
  // 使用 crypto 库生成随机字节并将其转换为字符串
  const buffer = crypto.randomBytes(length);
  return buffer.toString('hex').slice(0, length).toUpperCase(); // 将代码转换为大写
}

/**
 * 生成唯一的用户名
 * @returns 唯一用户名
 */
export function generateUniqueUsername(): string {
  // 生成一个更随机的用户名，例如结合时间戳和随机字符串
  return `user_${Date.now().toString(36)}${Math.random().toString(36).substring(2, 7)}`;
}
