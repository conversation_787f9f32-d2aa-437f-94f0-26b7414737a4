import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { QueueService } from './queue.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: (configService: ConfigService) => {
        const Redis = require('ioredis');
        const redisConfig = configService.get('redis');

        const redisClient = new Redis({
          host: redisConfig.host,
          port: redisConfig.port,
          password: redisConfig.password,
          db: redisConfig.db,
          ...redisConfig.options,
        });

        // 添加事件监听器
        redisClient.on('connect', () => {
          console.log('[NestJS Redis] 已连接');
        });

        redisClient.on('ready', () => {
          console.log('[NestJS Redis] 准备就绪');
        });

        redisClient.on('error', (error) => {
          console.error('[NestJS Redis] 错误:', error);
        });

        redisClient.on('close', () => {
          console.log('[NestJS Redis] 连接已关闭');
        });

        redisClient.on('reconnecting', () => {
          console.log('[NestJS Redis] 正在重新连接...');
        });

        return redisClient;
      },
      inject: [ConfigService],
    },
    RedisService,
    QueueService,
  ],
  exports: ['REDIS_CLIENT', RedisService, QueueService],
})
export class RedisModule {}
