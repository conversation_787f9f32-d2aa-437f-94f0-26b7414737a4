# Wolf.Fun NestJS API

这是Wolf.Fun项目的NestJS重构版本，提供现代化的API架构和更好的可维护性。

## 功能特性

- 🚀 基于NestJS框架的现代化架构
- 🔐 JWT认证和钱包认证支持
- 🗄️ Sequelize ORM数据库集成
- 🔄 Redis缓存和队列支持
- 📚 Swagger API文档
- 🌐 多语言支持 (nestjs-i18n)
- 🛡️ 全局异常处理和响应拦截器
- 🔤 完整的国际化解决方案

## 快速开始

### 安装依赖

**方法一：使用安装脚本（推荐）**

Windows:
```bash
cd @nestjs
install-dependencies.bat
```

Linux/macOS:
```bash
cd @nestjs
chmod +x install-dependencies.sh
./install-dependencies.sh
```

**方法二：手动安装**
```bash
cd @nestjs
npm install
```

### 环境配置

复制环境变量模板文件：

```bash
cp .env.example .env
```

根据你的环境配置修改 `.env` 文件中的数据库和Redis连接信息。

### 启动应用

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### API文档

启动应用后，访问 `http://localhost:3001/api/docs` 查看Swagger API文档。

### 多语言测试

```bash
# 中文简体
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/health

# 中文繁体
curl -H "Accept-Language: zh-TW,zh-tw;q=0.9,zh;q=0.8,en;q=0.7" http://localhost:3001/api/health

# 日语
curl -H "Accept-Language: ja-JP,ja;q=0.9,en;q=0.8" http://localhost:3001/api/health

# I18n 功能测试
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/i18n-test

# 运行自动化测试
node test-i18n.js
```

## 项目结构

```
src/
├── auth/                 # 认证模块
│   ├── guards/          # 认证守卫
│   ├── strategies/      # 认证策略
│   └── ...
├── common/              # 通用模块
│   ├── filters/         # 异常过滤器
│   ├── interceptors/    # 拦截器
│   ├── middleware/      # 中间件
│   └── services/        # 通用服务
├── config/              # 配置模块
├── database/            # 数据库模块
├── i18n/                # 国际化模块
│   ├── en/             # 英语翻译文件
│   ├── zh/             # 中文翻译文件
│   ├── zh-tw/          # 繁体中文翻译文件
│   ├── ja/             # 日语翻译文件
│   └── ...
├── models/              # 数据模型
├── redis/               # Redis模块
├── app.module.ts        # 应用主模块
└── main.ts             # 应用入口
```

## 开发指南

### 添加新模块

使用NestJS CLI生成新模块：

```bash
nest generate module <module-name>
nest generate controller <controller-name>
nest generate service <service-name>
```

### 数据库模型

数据库模型将在后续阶段根据现有Express应用的模型进行迁移。

### API接口重构

API接口将按照以下原则进行重构：

1. 保持现有功能不变
2. 使用NestJS装饰器和依赖注入
3. 添加适当的验证和文档
4. 遵循RESTful设计原则

## 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 部署

```bash
# 构建生产版本
npm run build

# 启动生产服务
npm run start:prod
```

## 第一阶段完成状态

✅ **已完成的功能：**

1. **基础框架搭建**
   - NestJS 项目结构初始化
   - TypeScript 配置
   - 基本的模块、控制器、服务结构

2. **配置管理**
   - 环境变量配置系统
   - 应用、数据库、Redis 配置模块
   - 配置验证模式

3. **数据库集成**
   - Sequelize ORM 集成
   - 基础模型定义（User, UserWallet）
   - 数据库连接配置

4. **Redis 集成**
   - Redis 连接配置
   - BullMQ 队列系统集成
   - 队列服务封装

5. **认证系统**
   - JWT 认证策略
   - 钱包认证守卫
   - 认证中间件

6. **通用功能**
   - 语言中间件
   - 全局异常过滤器
   - 响应拦截器
   - 日志服务
   - 翻译服务

7. **开发工具**
   - Swagger API 文档
   - 装饰器（用户、语言）
   - 请求上下文服务

## 下一步：第二阶段

第二阶段将根据您的指示，逐个重构现有的API接口。请指定您希望首先重构的接口，我将：

1. 分析现有接口的功能和依赖
2. 创建对应的NestJS模块、控制器、服务
3. 保持相同的功能和行为
4. 遵循NestJS最佳实践

请告诉我您希望首先重构哪个API接口。

## 多语言国际化 (I18n)

本项目已集成完整的多语言支持：

### 支持的语言
- 🇺🇸 英语 (en) - 默认语言
- 🇨🇳 中文简体 (zh)
- 🇹🇼 中文繁体 (zh-tw)
- 🇯🇵 日语 (ja)

### 特性
- ✅ 基于 nestjs-i18n 的现代化解决方案
- ✅ 简化的语言检测逻辑，只使用 Accept-Language 请求头
- ✅ 智能语言匹配和质量值排序
- ✅ 自动翻译错误消息和验证消息
- ✅ 支持变量插值
- ✅ 向后兼容现有翻译函数

### 详细文档
查看 [I18N_GUIDE.md](./I18N_GUIDE.md) 获取完整的使用指南和API文档。
