// src/i18n/locales/ja.ts
export default {
  iap: {
    productNotFound: '商品が見つかりません',
    purchaseSuccess: '購入が正常に完了しました',
    paymentFailed: '支払いに失敗しました',
    dailyTypeLimitReached: '%{productType}商品の1日の購入制限に達しました。1日に1つの%{productType}商品のみ購入できます。',
    dailyLimitReached: '商品の1日の購入制限に達しました：%{productName}',
    accountLimitReached: '商品のアカウント購入制限に達しました：%{productName}',
    vipAlreadyActive: 'VIPメンバーシップは既にアクティブです',
    priceNotAvailable: '選択した支払い方法は商品で利用できません：%{productName}',
    walletIdRequired: 'ウォレットIDが必要です',
    missingRequiredParameters: '必須パラメータが不足しています',
    walletNotFound: 'ウォレットが見つかりません',
    dappPortalConfigMissing: 'DappPortal設定が不足しています',
    userWalletAddressNotFound: 'ユーザーウォレットアドレスが見つかりません',
    failedToCreatePaymentOrder: '支払い注文の作成に失敗しました',
    dappPortalUnavailable: 'DappPortalサービスが利用できません',
    invalidPaymentResponse: '支払いサービスからの無効な応答',
    walletIdAndBoosterIdRequired: 'ウォレットIDとブースターIDが必要です',
    boosterNotFoundOrInsufficient: 'ブースターが見つからないか数量が不足しています',
    sameTypeBoosterAlreadyActive: '同じタイプのブースターが既にアクティブです',
    products: {
      speed_boost_x2_1hr: {
        description: '1時間マイニング速度を2倍にします。素早い進歩ブーストに最適！'
      },
      speed_boost_x2_24hr: {
        description: '24時間マイニング速度を2倍にします。日々の収益を最大化！'
      },
      speed_boost_x4_1hr: {
        description: '1時間マイニング速度を4倍にします。急速な利益のための究極のパワーブースト！'
      },
      speed_boost_x4_24hr: {
        description: '24時間マイニング速度を4倍にします。究極の日々のアドバンテージ！'
      },
      time_warp_1hr: {
        description: '1時間の待機時間を即座にスキップします。今すぐ報酬を獲得！'
      },
      time_warp_24hr: {
        description: '24時間の待機時間を即座にスキップします。丸一日先に進む！'
      },
      vip_membership: {
        description: '専用VIP特典をアンロック：高い制限、特別報酬、プレミアム機能！'
      },
      special_offer_bundle: {
        description: '期間限定バンドル、驚きの価値！複数のブースターと専用アイテムを含みます。'
      },
      // Generic product type descriptions
      speed_boost: {
        description: 'マイニング速度をブーストして、より速い進歩と高い収益を！'
      },
      time_warp: {
        description: '待機時間を即座にスキップして、今すぐ報酬を獲得！'
      },
      special_offer: {
        description: '期間限定特別オファー、驚きの価値！'
      }
    }
  },
  errors: {
    auth: {
      invalidToken: '無効または期限切れのトークン',
      unauthorized: '不正なアクセス',
      userNotFound: 'ユーザーが見つかりません',
      invalidCredentials: '無効な認証情報',
      tokenExpired: 'トークンが期限切れです',
      insufficientPermissions: '権限が不足しています'
    },
    iap: {
      dailyTypeLimitReached: '%{productType}商品の1日の購入制限に達しました。1日に1つの%{productType}商品のみ購入できます。',
      dailyLimitReached: '商品の1日の購入制限に達しました：%{productName}',
      accountLimitReached: '商品のアカウント購入制限に達しました：%{productName}',
      vipAlreadyActive: 'VIPメンバーシップは既にアクティブです',
      priceNotAvailable: '選択した支払い方法は商品で利用できません：%{productName}',
      walletIdRequired: 'ウォレットIDが必要です',
      missingRequiredParameters: '必須パラメータが不足しています',
      productNotFound: '商品が見つからないか非アクティブです',
      walletNotFound: 'ウォレットが見つかりません',
      dappPortalConfigMissing: 'DappPortal設定が不足しています',
      userWalletAddressNotFound: 'ユーザーウォレットアドレスが見つかりません',
      failedToCreatePaymentOrder: '支払い注文の作成に失敗しました',
      dappPortalUnavailable: 'DappPortalサービスが利用できません',
      invalidPaymentResponse: '支払いサービスからの無効な応答',
      walletIdAndBoosterIdRequired: 'ウォレットIDとブースターIDが必要です',
      boosterNotFoundOrInsufficient: 'ブースターが見つからないか数量が不足しています',
      sameTypeBoosterAlreadyActive: '同じタイプのブースターが既にアクティブです'
    },
    unauthorized: "認証されていません",
    insufficientFragments: "フラグメントが不足しています",
    invalidFragmentType: "無効なフラグメントタイプ",
    noToken: "トークンが提供されていません",
    noTokenProvided: "トークンが提供されていません",
    invalidTokenFormat: "無効なトークン形式",
    paramValidation: "パラメータ検証に失敗しました",
    walletBound: "ウォレットアドレスは既に他のアカウントにバインドされています",
    singleWallet: "各TGアカウントは1つのウォレットのみバインドできます",
    uniqueCodeGeneration: "一意の招待コードの生成に失敗しました。再試行してください",
    unknown: "不明なエラーが発生しました",
    invalidLimit: "無効な制限値",
    invalidFields: "フィールドと追加する金額を次の形式で提供してください：%{fieldName: amount}",
    invalidFieldNames: "無効なフィールド：%{fields}、有効なフィールドは：%{validFields}",
    invalidFieldValues: "次のフィールドに無効な値があります：%{fields}、すべての値が0より大きい数値であることを確認してください",
    noValidFieldsToUpdate: "更新する有効なフィールドがありません",
    missingInitData: "initDataが不足しています",
    invalidTelegramUserId: "無効なTelegramユーザーID",
    invalidTelegramData: "無効なTelegramデータ",
    userDataNotFound: "ユーザーデータが見つかりません",
    authenticationFailed: "認証に失敗しました",
    sourceUserNotFound: "ソースユーザーが見つかりません",
    invitationCodeNotExist: "招待コードが存在しません",
    userNotFound: "ユーザーが見つかりません",
    notEnoughChests: "開くのに十分なチェストがありません。必要：%{required}、利用可能：%{available}",
    chestOpenCountInvalid: "チェストを開く数は1または10のみです。取得値：%{count}",
    alreadyHaveReferrer: "既に紹介者がいます。再度バインドできません。",
    walletAlreadyHasReferrer: "このウォレットには既に紹介者がいます。再度バインドできません。",
    cannotUseOwnCode: "自分のコードを紹介者として使用できません",
    cannotUseOwnWalletCode: "自分のウォレットコードを紹介者として使用できません",
    taskNotFound: "タスクが見つかりません",
    taskOnlyOnce: "このタスクは一度だけ完了できます",
    taskAlreadyCompleted: "今日のタスクは既に完了しています。",
    getTaskListFailed: "タスクリストの取得に失敗しました",
    completeTaskFailed: "タスクの完了に失敗しました",
    quantityMustBePositive: "数量は正の値である必要があります",
    insufficientBalance: "残高不足のため、チケットを購入できません。",
    alreadyClaimedToday: "今日は既に請求済みです",
    notEnoughInvites: "日次ボックスを請求するのに十分な招待がありません",
    apiKeyRequired: "TONCENTER_API_KEYが必要です",
    invalidPayloadLength: "無効なペイロード長、取得値：%{length}、期待値：32",
    invalidPayloadSignature: "無効なペイロード署名",
    payloadExpired: "ペイロードが期限切れです",
    tonProofExpired: "TON証明が期限切れです",
    proofTimestampTooOld: "証明のタイムスタンプが古すぎます",
    checkProofErrorPublicKeyNotFound: "CheckProofエラー：公開鍵が見つかりません",
    checkProofErrorPublicKeyMismatch: "CheckProofエラー：公開鍵が一致しません",
    checkProofErrorAddressMismatch: "CheckProofエラー：アドレスが一致しません",
    domainLengthMismatch: "ドメイン長が提供された長さバイト%{lengthBytes}と一致しません",
    roomLockFailed: "ルーム割り当てロックの取得に失敗しました。後でもう一度お試しください",
    getRoomIdFailed: "現在のルームIDの取得に失敗しました：%{error}",
    luaScriptFailed: "Luaスクリプトの実行に失敗しました：%{error}",
    roomExceedsLimit: "ルーム%{roomId}が制限を超えています：%{count}",
    roomAllocationFailed: "ルーム割り当てに失敗しました",
    roomFull: "ルーム%{roomId}は満員です",
    walletInfoNotFound: "ウォレット情報が見つかりません",
    withdrawalAmountEmpty: "出金額を空にすることはできません",
    withdrawalAddressNotFound: "有効なウォレットアドレスが見つかりません。出金アドレスを提供してください",
    getPublicKeyNotImplemented: "公開鍵の取得は実装されていません",
    publicKeyNotMatch: "公開鍵が一致しません",
    stateInitNotMatch: "状態初期化が一致しません",
    timestampNotMatch: "タイムスタンプが一致しません",
    noRewardsToCollect: "収集する報酬がありません",
    userWalletNotFound: "ユーザーウォレットが見つかりません",
    noBullKingRewards: "請求するBull King報酬がありません",
    notCompletedThreeRounds: "今日3ラウンドを完了していません。リベートを請求できません",
    withdrawalMinAmount: "出金額は%{minAmount} %{currency}未満にできません",
    withdrawalDailyLimitReached: "1日の出金制限%{dailyLimit}回に達しました",
    insufficientFundsWithFee: "資金不足、%{totalAmount} %{currency}が必要です（%{fee} %{currency}手数料を含む）",
    insufficientUnlockedMOOF: "ロック解除されたMOOF残高が不足しています。%{totalAmount} MOOF（%{fee} MOOF手数料を含む）が必要です",
    userNotExist: "ユーザーが存在しません",
    invalidEmailFormat: "無効なメール形式",
    emailAlreadyBound: "このメールは既に他のユーザーにバインドされています",
    receiverWalletAddressEmpty: "受信者ウォレットアドレスを空にすることはできません",
    transferAmountMustBePositive: "転送額は0より大きい必要があります",
    senderWalletNotExist: "送信者ウォレットが存在しません",
    receiverWalletNotExist: "受信者ウォレットが存在しません",
    cannotTransferToSelf: "自分自身に転送することはできません",
    transferFailedInsufficientBalance: "転送に失敗しました。残高不足の可能性があります",
    insufficientFreeTickets: "無料チケットが不足しています",
    freeTicketAmountMustBePositiveInteger: "無料チケット数は正の整数である必要があります",
    freeTicketDailyLimitReached: "1日の無料チケット転送制限に達しました。制限：%{dailyLimit}、残り：%{remaining}",
    transferFailedInsufficientFreeTickets: "転送に失敗しました。無料チケットが不足しています",
    transferFreeTicketFailed: "無料チケットの転送に失敗しました",
    refundFailed: "返金操作に失敗しました。ウォレット：%{walletId}",
    multipleRefundsFailed: "1つ以上の返金操作に失敗しました",
    winnerOperationFailed: "勝者操作に失敗しました。ウォレット：%{walletId}",
    loserOperationFailed: "敗者操作に失敗しました。ウォレット：%{walletId}",
    multipleLoserOperationsFailed: "1つ以上の敗者操作に失敗しました",
    invalidTaskData: "無効なタスクデータ",
    winnerNotFound: "勝者が見つかりません",
    getKolRewardsFailed: "KOL報酬の取得に失敗しました",
    claimKolRewardFailed: "KOL報酬の請求に失敗しました",
    getKolStatusFailed: "KOLステータスの取得に失敗しました",
    rewardNotFoundOrClaimed: "報酬が見つからないか既に請求済みです",
    getBullKingLeaderboardFailed: "Bull Kingリーダーボードの取得に失敗しました",
    getMoofHoldersLeaderboardFailed: "MOOF保有者リーダーボードの取得に失敗しました",
    // Web3ウォレットログイン関連エラー
    invalidSignature: "無効なウォレット署名",
    invalidNonce: "無効または期限切れのnonce",
    loginFailed: "Web3ウォレットログインに失敗しました",
    failedToGenerateUniqueCode: "一意の招待コードの生成に失敗しました",
    invalidAccelerationSeconds: "有効な加速秒数を提供してください",
    accelerationExceedsLimit: "単一の加速は10秒を超えることはできません",
    accelerationLimitExceeded: "1日の加速時間制限に達しました",
    countdownNotFound: "カウントダウン記録が見つかりません",
    countdownNotActive: "チェストカウントダウンがアクティブでないか終了しています。加速できません",
    claimMoofHoldersRewardFailed: "MOOF保有者報酬の請求に失敗しました",
    claimBullKingRewardFailed: "Bull King報酬の請求に失敗しました",
    getPersonalKolLeaderboardFailed: "個人KOLリーダーボードの取得に失敗しました",
    claimPersonalKolRewardFailed: "個人KOL報酬の請求に失敗しました",
    getTeamKolLeaderboardFailed: "チームKOLリーダーボードの取得に失敗しました",
    claimTeamKolRewardFailed: "チームKOL報酬の請求に失敗しました",
    getPersonalKolProgressFailed: "個人KOL進捗の取得に失敗しました",
    getTeamKolProgressFailed: "チームKOL進捗の取得に失敗しました",
    getDailyPromotionProgressFailed: "日次プロモーション進捗の取得に失敗しました",
    invalidPagination: "無効なページネーションパラメータ",
    missingWalletId: "ウォレットIDが不足しています",
    missingUserId: "ユーザーIDが不足しています",
    missingUserOrWalletId: "ユーザーIDまたはウォレットIDが不足しています",
    serverError: "内部サーバーエラー",
    roomDetailsNotFound: "ルーム詳細が見つかりません",
    getDailyRebateDetailsFailed: "日次リベート詳細の取得に失敗しました",
    getPendingRebateAmountFailed: "保留中のリベート額の取得に失敗しました",
    requestInProgress: "別のリクエストが進行中です。後でもう一度お試しください",
    noSessionsProvided: "少なくとも1つのセッションを提出してください",
    invalidSessionFormat: "各セッションにはsession_dt、session_category、および少なくとも1つのラウンドが含まれている必要があります",
    sessionNotFound: "セッション%{category} %{date}が存在しません",
    insufficientBalanceForReservation: "すべてのセッションを予約するのに十分なチケットまたはUSD残高がありません",
    databaseConnectionError: "データベース接続エラー",
    invalidDateFormat: "無効な日付形式です。%{format}を使用してください",
    invalidDate: "無効な日付",
    dateNotToday: "日付は今日である必要があります",
    userInfoFailed: "ユーザー情報の取得に失敗しました",
    sendEmailCodeFailed: "メール確認コードの送信に失敗しました",
    bindEmailFailed: "メールのバインドに失敗しました",
    verificationCodeExpired: "確認コードが期限切れです",
    incorrectVerificationCode: "確認コードが正しくありません",
    emailNotBound: "まだメールをバインドしていません。最初にメールをバインドしてください",
    sendTransferCodeFailed: "転送確認コードの送信に失敗しました",
    transferFailed: "転送に失敗しました",
    getWalletHistoryFailed: "ウォレット履歴の取得に失敗しました",
    getWithdrawalSettingsFailed: "出金設定の取得に失敗しました",
    withdrawUSDFailed: "USD出金に失敗しました",
    withdrawMOOFFailed: "MOOF出金に失敗しました",
    withdrawTONFailed: "TON出金に失敗しました",
    getMoofBalancesFailed: "MOOF残高の取得に失敗しました",
    getChestRewardsFailed: "チェスト報酬の取得に失敗しました",
    openChestsFailed: "チェストを開くのに失敗しました",
    getBullUnlockHistoryFailed: "MOOFアンロック履歴の取得に失敗しました",
    roomNotFound: "ルームが見つかりません",
    getRoomDetailsFailed: "ルーム詳細の取得に失敗しました",
    chestNotAvailableYet: "チェストはまだ収集できません",
    jackpotPoolNotFound: "ジャックポットプールが見つかりません",
    shareLinkNotFound: "シェアブーストリンクが見つかりません",
    shareLinkExpired: "シェアブーストリンクが期限切れです",
    shareLinkMaxUsesReached: "シェアブーストリンクが最大使用回数に達しました",
    cannotBoostYourself: "自分自身をブーストすることはできません",
    alreadyUsedShareLink: "このシェアブーストリンクは既に使用済みです",
    missingChestId: "チェストIDが不足しています",
    missingShareCode: "シェアコードが不足しています",
    invalidAutoCollectValue: "無効な自動収集値",
    noReferralChests: "利用可能な紹介チェストがありません",
    alreadyCollectedFourChests: "4つのチェストボーナスは既に収集済みです",
    pendingMilkAmountMustBeNonNegative: "保留中のミルク量は非負の数である必要があります",
    gemAmountMustBePositive: "ジェム量は正の数である必要があります",
    milkAmountMustBePositive: "ミルク量は正の数である必要があります",
    invalidAmount: "無効な量",
    increaseMilkFailed: "保留中のミルクの増加に失敗しました",
    deliveryLineNotExist: "配送ラインが存在しません",
    deliveryLineNotFound: "配送ラインが見つかりません",
    insufficientMilk: "ミルクが不足しています",
    notEnoughPendingMilk: "保留中のミルクが不足しています",
    increaseGemFailed: "ジェムの増加に失敗しました",
    getOfflineRewardFailed: "オフライン報酬の取得に失敗しました",
    notOffline: "オフラインではありません",
    userNeverActive: "ユーザーは一度もアクティブになったことがありません",
    userStillOnline: "ユーザーはまだオンラインです",
    claimOfflineRewardFailed: "オフライン報酬の請求に失敗しました",
    batchUpdateResourcesFailed: "バッチリソース更新に失敗しました",
    // テストリセット関連エラー
    rateLimitExceeded: "リクエストが多すぎます。後でもう一度お試しください",
    forbidden: "権限が不足しています"
  },
  success: {
    ticketPurchase: "チケット購入成功",
    fragmentCraft: "フラグメント作成成功",
    proofGeneration: "証明生成成功",
    iap: {
      boosterActivated: "%{boosterType}ブースターが正常にアクティベートされました"
    },
    claimMoofHoldersReward: "MOOF保有者報酬を正常に請求しました",
    claimBullKingReward: "Bull King報酬を正常に請求しました",
    claimPersonalKolReward: "個人KOL報酬を正常に請求しました",
    claimTeamKolReward: "チームKOL報酬を正常に請求しました",
    chestAccelerated: "チェストカウントダウンが正常に加速されました",
    getPersonalKolProgress: "個人KOL進捗を正常に取得しました",
    getTeamKolProgress: "チームKOL進捗を正常に取得しました",
    getDailyPromotionProgress: "日次プロモーション進捗を正常に取得しました",
    taskCompleted: "タスクが正常に完了しました",
    addTestCoinAllAccounts: "すべてのアカウントに次のフィールドを正常に追加しました：%{fields}",
    addTestCoinYourAccount: "あなたのアカウントに次のフィールドを正常に追加しました：%{fields}",
    getBullUnlockHistory: "MOOFアンロック履歴を正常に取得しました",
    chestOpened: "チェストを正常に開きました",
    chestCount: "チェスト数を正常に取得しました",
    dailyChestsClaimed: "%{count}個の日次チェストを正常に請求しました",
    getGameSessionRanking: "ゲームセッションランキングを正常に取得しました",
    getGameSessionLeaderboard: "ゲームセッションリーダーボードを正常に取得しました",
    craftTicket: "チケットを正常に作成しました",
    transferFreeTicketSuccess: "無料チケットを正常に転送しました",
    getRemainingTransferLimit: "残り転送制限を正常に取得しました",
    getAllSessions: "すべてのセッションを正常に取得しました",
    getGameHistory: "ゲーム履歴を正常に取得しました",
    getRoomDetails: "ルーム詳細を正常に取得しました",
    getPersonalKolStats: "個人KOL統計を正常に取得しました",
    getTeamKolStats: "チームKOL統計を正常に取得しました",
    getPersonalKolHistory: "個人KOL履歴を正常に取得しました",
    getTeamKolHistory: "チームKOL履歴を正常に取得しました",
    getMyReservations: "私の予約を正常に取得しました",
    getDailyRebateDetails: "日次リベート詳細を正常に取得しました",
    getPendingRebateAmount: "保留中のリベート額を正常に取得しました",
    claimRebateSuccess: "リベートを正常に請求しました",
    referralBound: "紹介コードを正常にバインドしました",
    referralList: "紹介リストを正常に取得しました",
    referralStatus: "紹介ステータスを正常に取得しました",
    reservationSuccess: "予約成功",
    getReservations: "予約を正常に取得しました",
    getKolRewards: "KOL報酬を正常に取得しました",
    claimKolReward: "KOL報酬を正常に請求しました",
    getKolStatus: "KOLステータスを正常に取得しました",
    emailCodeSent: "メール確認コードを正常に送信しました",
    emailBound: "メールを正常にバインドしました",
    transferCodeSent: "転送確認コードを正常に送信しました",
    transferSuccess: "転送が正常に完了しました",
    getWalletHistory: "ウォレット履歴を正常に取得しました",
    getWithdrawalSettings: "出金設定を正常に取得しました",
    withdrawUSDSuccess: "USDを正常に出金しました",
    withdrawMOOFSuccess: "MOOFを正常に出金しました",
    withdrawTONSuccess: "TONを正常に出金しました",
    getMoofBalancesSuccess: "MOOF残高を正常に取得しました",
    getChestRewards: "チェスト報酬を正常に取得しました",
    openChests: "チェストを正常に開きました",
    increaseMilk: "保留中のミルクを正常に増加しました",
    increaseGem: "ジェムを正常に増加しました",
    milkToGem: "ミルクからジェムへの変換が成功しました",
    getOfflineReward: "オフライン報酬を正常に取得しました",
    claimOfflineReward: "オフライン報酬を正常に請求しました",
    batchUpdateResources: "バッチリソース更新が成功しました",
    // テストリセット関連成功メッセージ
    gameStateReset: "ゲーム状態を正常にリセットしました"
  },
  validation: {
    quantity: {
      positive: "数量は正の整数である必要があります"
    }
  },
  referral: {
    dailyChestReward: "日次請求%{count}個のミステリーチェスト"
  },
  labels: {
    notQualified: "資格なし",
    oneStarKol: "一つ星KOL",
    twoStarKol: "二つ星KOL",
    threeStarKol: "三つ星KOL",
    silverKol: "シルバーKOL",
    goldKol: "ゴールドKOL",
    claimed: "請求済み",
    canClaim: "請求可能"
  },
  tasks: {
    dailySignin: "日次報酬チェスト",
    joinTelegram: "私たちのTelegramチャンネルに参加チェスト",
    followTwitter: "私たちのXをフォローチェスト"
  },
  email: {
    verificationCode: {
      subject: "MoofFun メール確認コード",
      title: "メール確認",
      binding: "メールアドレスをバインドしています。次の確認コードを使用してプロセスを完了してください："
    },
    transfer: {
      subject: "MoofFun 転送確認コード",
      title: "転送確認",
      operation: "転送を開始しています。次の詳細を確認し、確認コードを使用して確認してください：",
      amount: "転送額",
      receiverAddress: "受信者アドレス"
    },
    common: {
      greeting: "親愛なるユーザー様、",
      verificationCode: "確認コード",
      codeValidity: "この確認コードは5分間有効です。",
      ignoreMessage: "この操作をリクエストしていない場合は、このメールを無視してください。",
      autoSendNotice: "これは自動送信メッセージです。返信しないでください。"
    },
    rebate: {
      title: "チケットリベート決済通知",
      content: "%{count}件のチケットリベートが決済され、合計%{amount} USDがあなたのアカウントに追加されました。",
      displayRemark: "チケットリベートを請求",
      developerRemark: "%{count}件の保留中のチケットリベートを請求しました"
    }
  },
  walletHistory: {
    transferOut: "%{amount} USDを%{address}に転送",
    transferInDev: "%{address}からの転送を受信",
    transferIn: "%{address}から%{amount} USDを受信",
    transferOutDev: "%{address}にUSDを転送",
    bet: "ベット",
    betSession: "セッション%{sessionNumber} ラウンド%{roundIndex}にベット",
    withdrawal: "出金",
    withdrawalUSD: "アドレス%{address}にUSD出金",
    withdrawalUSDDev: "USD出金、金額：%{amount}、手数料：%{fee}、ステータス：%{status}",
    withdrawalMOOF: "アドレス%{address}にMOOF出金",
    withdrawalMOOFDev: "MOOF出金、金額：%{amount}、手数料：%{fee}",
    withdrawalTON: "アドレス%{address}にTON出金",
    withdrawalTONDev: "TON出金、金額：%{amount}、手数料：%{fee}",
    transferFreeTicketOut: "%{amount}枚の無料チケットを%{address}に転送",
    transferFreeTicketIn: "%{address}から%{amount}枚の無料チケットを受信",
    transferFreeTicketOutDev: "%{address}に無料チケットを転送",
    transferFreeTicketInDev: "%{address}から無料チケットを受信",
    reference: {
      increasePendingMilk: "保留中のミルクを増加",
      increaseGem: "ジェムを増加",
      convertMilkToGem: "ミルクをジェムに変換",
      offlineReward: "オフライン報酬"
    },
    feDisplayRemark: {
      increasedPendingMilk: "%{amount}の保留中のミルクを増加しました",
      increasedGem: "%{amount}のジェムを増加しました",
      milkConvertedToGem: "%{amount}のミルクをジェムに変換しました",
      offlineGemReward: "オフライン報酬から%{amount}のジェムを受け取りました"
    }
  },
  chest: {
    level1: "レベル1チェスト",
    level2: "レベル2チェスト",
    level3: "レベル3チェスト",
    level4: "レベル4レアチェスト",
    rewards: {
      ticket: "チケット",
      fragment_green: "グリーンフラグメント",
      fragment_blue: "ブルーフラグメント",
      fragment_purple: "パープルフラグメント",
      fragment_gold: "ゴールドフラグメント",
      ton: "TON",
      gem: "GEM"
    },
    announcement: {
      title: "レアチェスト報酬！",
      content: "プレイヤーID：%{userId}がレベル4レアチェストを開いたことをお祝いします！報酬：チケット x1、TON x 0.1、チケットフラグメント x 5、GEM x 1000"
    },
    summary: {
      level1: "%{count}個のレベル1チェストを開きました",
      level2: "%{count}個のレベル2チェストを開きました",
      level3: "%{count}個のレベル3チェストを開きました",
      level4: "%{count}個のレベル4チェストを開きました"
    },
    error: {
      fieldNotExist: "ウォレットに報酬フィールドが存在しません：%{field}",
      updateFailed: "ウォレットフィールドの更新に失敗しました：%{field}"
    }
  },
  jackpot: {
    announcement: {
      levelUp: {
        title: "ジャックポットチェストアップグレード",
        content: "おめでとうございます！ジャックポットチェストがレベル%{level}にアップグレードし、プール金額%{amount} TONが請求されました。現在レベル%{nextLevel}に入りました！"
      },
      winner: {
        title: "ジャックポットチェスト大賞",
        content: "ユーザー%{userId}がレベル%{level}ジャックポットチェスト大賞%{amount} TONを獲得したことをお祝いします！"
      }
    }
  }
}
