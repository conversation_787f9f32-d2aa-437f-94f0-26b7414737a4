import { Withdrawal } from "../models";
import { Transaction } from "sequelize";

/**
 * 更新提现记录的区块链交易信息
 * @param withdrawalId 提现记录ID
 * @param txInfo 区块链交易信息
 * @param transaction 事务对象（可选）
 * @returns 更新后的提现记录
 */
export async function updateWithdrawalTxInfo(
  withdrawalId: number,
  txInfo: {
    txHash: string;
    blockNumber?: number;
    confirmations?: number;
    txStatus: 'pending' | 'confirmed' | 'failed';
    txTimestamp?: Date;
    networkFee?: number;
  },
  transaction?: Transaction
) {
  const withdrawal = await Withdrawal.findByPk(withdrawalId, { transaction });
  
  if (!withdrawal) {
    throw new Error(`提现记录不存在: ${withdrawalId}`);
  }
  
  // 更新区块链交易信息
  withdrawal.txHash = txInfo.txHash;
  if (txInfo.blockNumber !== undefined) withdrawal.blockNumber = txInfo.blockNumber;
  if (txInfo.confirmations !== undefined) withdrawal.confirmations = txInfo.confirmations;
  withdrawal.txStatus = txInfo.txStatus;
  if (txInfo.txTimestamp) withdrawal.txTimestamp = txInfo.txTimestamp;
  if (txInfo.networkFee !== undefined) withdrawal.networkFee = txInfo.networkFee;
  
  // 如果交易已确认，更新提现状态为completed
  if (txInfo.txStatus === 'confirmed' && withdrawal.status === 'approved') {
    withdrawal.status = 'completed';
    withdrawal.processedAt = new Date();
  }
  
  // 如果交易失败，更新提现状态为failed
  if (txInfo.txStatus === 'failed') {
    withdrawal.status = 'failed';
    withdrawal.processedAt = new Date();
  }
  
  await withdrawal.save({ transaction });
  
  return withdrawal;
}

/**
 * 更新提现记录的确认数
 * @param txHash 交易哈希
 * @param confirmations 确认数
 * @returns 更新后的提现记录
 */
export async function updateWithdrawalConfirmations(txHash: string, confirmations: number) {
  const withdrawal = await Withdrawal.findOne({ where: { txHash } });
  
  if (!withdrawal) {
    throw new Error(`未找到交易哈希对应的提现记录: ${txHash}`);
  }
  
  withdrawal.confirmations = confirmations;
  
  // 如果确认数达到阈值（例如12个确认），将状态更新为confirmed
  const CONFIRMATION_THRESHOLD = 12; // 可以根据不同币种设置不同的阈值
  
  if (confirmations >= CONFIRMATION_THRESHOLD && withdrawal.txStatus === 'pending') {
    withdrawal.txStatus = 'confirmed';
    
    // 如果提现状态为approved，则更新为completed
    if (withdrawal.status === 'approved') {
      withdrawal.status = 'completed';
      withdrawal.processedAt = new Date();
    }
  }
  
  await withdrawal.save();
  
  return withdrawal;
}