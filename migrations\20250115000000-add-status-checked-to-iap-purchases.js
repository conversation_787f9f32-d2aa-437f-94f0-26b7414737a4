'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('iap_purchases', 'statusChecked', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      comment: '是否已经通过API检查过支付状态'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('iap_purchases', 'statusChecked');
  }
};