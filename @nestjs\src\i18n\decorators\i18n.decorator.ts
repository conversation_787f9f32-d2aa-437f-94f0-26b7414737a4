import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

/**
 * 获取当前请求的语言
 */
export const I18nLang = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    
    // 优先从请求对象获取语言（兼容现有系统）
    if (request.language) {
      return request.language;
    }
    
    // 从 I18n 上下文获取语言
    try {
      const i18nContext = I18nContext.current();
      return i18nContext?.lang || 'en';
    } catch {
      return 'en';
    }
  },
);

/**
 * 获取翻译函数
 */
export const I18nTranslate = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    
    // 返回翻译函数
    return (key: string, options?: Record<string, any>) => {
      try {
        const i18nContext = I18nContext.current();
        if (i18nContext) {
          return i18nContext.translate(key, { args: options });
        }
        return key;
      } catch {
        return key;
      }
    };
  },
);

// 兼容现有系统的装饰器别名
export const Language = I18nLang;
export const GetLanguage = I18nLang;
export const Translate = I18nTranslate;
