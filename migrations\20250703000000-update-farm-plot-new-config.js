'use strict';

const { Sequelize } = require('sequelize');

// 新的配置数据
const FARM_PLOT_BARN_COUNT = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20];
const FARM_PLOT_PRODUCTION_SPEED = [5,4.75,4.51,4.28,4.07,3.86,3.67,3.49,3.31,3.15,2.99,2.84,2.7,2.56,2.43,2.31,2.2,2.09,1.98,1.88];
const FARM_PLOT_UNLOCK_COST = [0, 378250,1418625,3405000,13241250,7566500,15322500,26484000,62426250,30645750,55333125,87399000,170257500,78697000,135072000,204312000,359437500,160801250,268160625,395385000];

const FARM_PLOT_<PERSON>LK_PRODUCTION = [
  [1.0, 1.0, 1.0, 1.0, 1.1, 1.1, 1.1, 1.1, 1.2, 1.2, 1.2, 1.2, 1.3, 1.3, 1.3, 1.3, 1.4, 1.4, 1.4, 1.4],
  [1.5, 1.5, 1.5, 1.5, 1.65, 1.65, 1.65, 1.65, 1.8, 1.8, 1.8, 1.8, 1.95, 1.95, 1.95, 1.95, 2.1, 2.1, 2.1, 2.1],
  [2.0, 2.0, 2.0, 2.0, 2.2, 2.2, 2.2, 2.2, 2.4, 2.4, 2.4, 2.4, 2.6, 2.6, 2.6, 2.6, 2.8, 2.8, 2.8, 2.8],
  [2.5, 2.5, 2.5, 2.5, 2.75, 2.75, 2.75, 2.75, 3.0, 3.0, 3.0, 3.0, 3.25, 3.25, 3.25, 3.25, 3.5, 3.5, 3.5, 3.5],
  [3.0, 3.0, 3.0, 3.0, 3.3, 3.3, 3.3, 3.3, 3.6, 3.6, 3.6, 3.6, 3.9, 3.9, 3.9, 3.9, 4.2, 4.2, 4.2, 4.2],
  [3.5, 3.5, 3.5, 3.5, 3.85, 3.85, 3.85, 3.85, 4.2, 4.2, 4.2, 4.2, 4.55, 4.55, 4.55, 4.55, 4.9, 4.9, 4.9, 4.9],
  [4.0, 4.0, 4.0, 4.0, 4.4, 4.4, 4.4, 4.4, 4.8, 4.8, 4.8, 4.8, 5.2, 5.2, 5.2, 5.2, 5.6, 5.6, 5.6, 5.6],
  [4.5, 4.5, 4.5, 4.5, 4.95, 4.95, 4.95, 4.95, 5.4, 5.4, 5.4, 5.4, 5.85, 5.85, 5.85, 5.85, 6.3, 6.3, 6.3, 6.3],
  [5.0, 5.0, 5.0, 5.0, 5.5, 5.5, 5.5, 5.5, 6.0, 6.0, 6.0, 6.0, 6.5, 6.5, 6.5, 6.5, 7.0, 7.0, 7.0, 7.0],
  [5.5, 5.5, 5.5, 5.5, 6.05, 6.05, 6.05, 6.05, 6.6, 6.6, 6.6, 6.6, 7.15, 7.15, 7.15, 7.15, 7.7, 7.7, 7.7, 7.7],
  [6.0, 6.0, 6.0, 6.0, 6.6, 6.6, 6.6, 6.6, 7.2, 7.2, 7.2, 7.2, 7.8, 7.8, 7.8, 7.8, 8.4, 8.4, 8.4, 8.4],
  [6.5, 6.5, 6.5, 6.5, 7.15, 7.15, 7.15, 7.15, 7.8, 7.8, 7.8, 7.8, 8.45, 8.45, 8.45, 8.45, 9.1, 9.1, 9.1, 9.1],
  [7.0, 7.0, 7.0, 7.0, 7.7, 7.7, 7.7, 7.7, 8.4, 8.4, 8.4, 8.4, 9.1, 9.1, 9.1, 9.1, 9.8, 9.8, 9.8, 9.8],
  [7.5, 7.5, 7.5, 7.5, 8.25, 8.25, 8.25, 8.25, 9.0, 9.0, 9.0, 9.0, 9.75, 9.75, 9.75, 9.75, 10.5, 10.5, 10.5, 10.5],
  [8.0, 8.0, 8.0, 8.0, 8.8, 8.8, 8.8, 8.8, 9.6, 9.6, 9.6, 9.6, 10.4, 10.4, 10.4, 10.4, 11.2, 11.2, 11.2, 11.2],
  [8.5, 8.5, 8.5, 8.5, 9.35, 9.35, 9.35, 9.35, 10.2, 10.2, 10.2, 10.2, 11.05, 11.05, 11.05, 11.05, 11.9, 11.9, 11.9, 11.9],
  [9.0, 9.0, 9.0, 9.0, 9.9, 9.9, 9.9, 9.9, 10.8, 10.8, 10.8, 10.8, 11.7, 11.7, 11.7, 11.7, 12.6, 12.6, 12.6, 12.6],
  [9.5, 9.5, 9.5, 9.5, 10.45, 10.45, 10.45, 10.45, 11.4, 11.4, 11.4, 11.4, 12.35, 12.35, 12.35, 12.35, 13.3, 13.3, 13.3, 13.3],
  [10.0, 10.0, 10.0, 10.0, 11.0, 11.0, 11.0, 11.0, 12.0, 12.0, 12.0, 12.0, 13.0, 13.0, 13.0, 13.0, 14.0, 14.0, 14.0, 14.0],
  [10.5, 10.5, 10.5, 10.5, 11.55, 11.55, 11.55, 11.55, 12.6, 12.6, 12.6, 12.6, 13.65, 13.65, 13.65, 13.65, 14.7, 14.7, 14.7, 14.7]
];

const FARM_PLOT_UPGRADE_COST = [
  [100, 315, 660, 1743, 1012, 1912, 3135, 6300, 3260, 5700, 8820, 15843, 7812, 13312, 20050, 33750, 16215, 27112, 40290, 65143],
  [9493, 14550, 19850, 38179, 15853, 24539, 33806, 58343, 24725, 38625, 53775, 87851, 37921, 59882, 84125, 133078, 58331, 92728, 131400, 203601],
  [28222, 42828, 57840, 109940, 44921, 68580, 93195, 158512, 65625, 100890, 138150, 222046, 93262, 144675, 199762, 310668, 132435, 207022, 288405, 439753],
  [59071, 89328, 120172, 227521, 92373, 140332, 189656, 320731, 131530, 200891, 273140, 435750, 180796, 278184, 380931, 587475, 246881, 382528, 528097, 798087],
  [104840, 158250, 212460, 401400, 162435, 246060, 331590, 559000, 228040, 346980, 469920, 746562, 307525, 470850, 641550, 984375, 410070, 631845, 867240, 1303050],
  [168322, 253783, 340290, 642051, 259267, 392090, 527377, 887287, 360742, 547593, 739665, 1171828, 480431, 733218, 995625, 1522378, 630382, 967545, 1322595, 0],
  [252300, 380100, 509250, 959859, 387075, 584634, 785362, 1319500, 535225, 811012, 1093450, 1729062, 706468, 1075640, 1457062, 2222343, 916162, 1402087, 1910775, 2850421],
  [359576, 541406, 724900, 1365426, 550048, 830053, 1113956, 1869656, 757047, 1145718, 1542585, 2435640, 992646, 1508718, 2039950, 3105300, 1275821, 1948196, 2648745, 3941798],
  [492945, 741892, 992850, 1869187, 752355, 1134573, 1521540, 2551725, 1031820, 1560015, 2098200, 3309187, 1345950, 2042831, 2758125, 4192312, 1717740, 2618392, 3553200, 5277431],
  [655183, 985725, 1318655, 2481557, 998205, 1504510, 2016397, 3379593, 1365097, 2062320, 2771405, 4366882, 1773321, 2688501, 3625537, 5503996, 2250251, 3425175, 4640707, 6881875],
  [849100, 1277115, 1707965, 3213000, 1291762, 1946109, 2607045, 4367300, 1762495, 2660962, 3573360, 5626468, 2281781, 3456140, 4656225, 7061512, 2881725, 4381177, 5928300, 8779575],
  [1077487, 1620253, 2166300, 4074117, 1637268, 2465732, 3301818, 5528812, 2229600, 3364425, 4515375, 7105195, 2878312, 4356351, 5864250, 8885812, 3620643, 5498971, 7432650, 10994812],
  [1343120, 2019300, 2699240, 5075250, 2038830, 3069540, 4109040, 6878100, 2771960, 4180920, 5608480, 8820625, 3569850, 5399400, 7263300, 10997850, 4475280, 6791040, 9170400, 13552175],
  [1648808, 2478472, 3312407, 6226860, 2500721, 3763943, 5037142, 8429025, 3395240, 5118997, 6863920, 10790085, 4363421, 6595973, 8867518, 13418418, 5454067, 8270032, 11158290, 16476107],
  [1997347, 3001961, 4011435, 7539581, 3027105, 4555136, 6094507, 10195650, 4104945, 6186915, 8292780, 13031296, 5266012, 7956393, 10690875, 16168612, 6565387, 9948420, 13413330, 19790859],
  [2391506, 3593921, 4801775, 9023723, 3622100, 5449449, 7289445, 12191825, 4906655, 7393113, 9906315, 15561296, 6284546, 9491123, 12747218, 19269206, 7817550, 11838864, 15952020, 23521050],
  [2834100, 4258575, 5689100, 10689843, 4290000, 6453112, 8630400, 14431625, 5806000, 8745900, 11715700, 18397968, 7426062, 11210812, 15050750, 22741312, 9219000, 13953937, 18791100, 27691125],
  [3327922, 5000152, 6678997, 12548320, 5034920, 7572521, 10125753, 16929018, 6808567, 10253801, 13732005, 21558468, 8697543, 13125754, 17615193, 26606081, 10778118, 16306211, 21947625, 32325300],
  [3875740, 5822726, 7777055, 14609718, 5861088, 8813784, 11783805, 19697837, 7919890, 11924962, 15966500, 25060406, 10105906, 15246515, 20454500, 30883875, 12503205, 18908133, 25438050, 37448228],
  [4480371, 6730561, 8988802, 16884515, 6772651, 10183365, 13612923, 22752175, 9145605, 13768001, 18430360, 28921062, 11658196, 17583464, 23582762, 35596021, 14402715, 21772389, 29279287, 43084354]
];

// 配置函数
function getFarmPlotBarnCount(level) {
  return FARM_PLOT_BARN_COUNT[level - 1];
}

function getFarmPlotProductionSpeed(level) {
  return FARM_PLOT_PRODUCTION_SPEED[level - 1];
}

function getFarmPlotUnlockCost(plotNumber) {
  return FARM_PLOT_UNLOCK_COST[plotNumber - 1];
}

function getFarmPlotMilkProduction(plotNumber, level) {
  return FARM_PLOT_MILK_PRODUCTION[plotNumber - 1][level - 1];
}

function getFarmPlotUpgradeCost(plotNumber, level) {
  return FARM_PLOT_UPGRADE_COST[plotNumber - 1][level - 1];
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始更新农场区块配置数据...');
    
    // 获取所有农场区块数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${farmPlots.length} 个农场区块记录`);
    
    let updatedCount = 0;
    
    // 批量更新农场区数据
    for (const plot of farmPlots) {
      let updates = {};
      let needsUpdate = false;
      
      // 重新计算牛舍数量
      const newBarnCount = plot.isUnlocked ? getFarmPlotBarnCount(plot.level) : 0;
      if (plot.barnCount !== newBarnCount) {
        updates.barnCount = newBarnCount;
        needsUpdate = true;
      }
      
      // 重新计算生产速度
      const newProductionSpeed = plot.isUnlocked ? getFarmPlotProductionSpeed(plot.level) : 5;
      if (Math.abs(plot.productionSpeed - newProductionSpeed) > 0.001) {
        updates.productionSpeed = newProductionSpeed;
        needsUpdate = true;
      }
      
      // 重新计算解锁费用
      const newUnlockCost = getFarmPlotUnlockCost(plot.plotNumber);
      if (Math.abs(plot.unlockCost - newUnlockCost) > 0.001) {
        updates.unlockCost = newUnlockCost;
        needsUpdate = true;
      }
      
      // 重新计算牛奶产量
      const newMilkProduction = plot.isUnlocked ? getFarmPlotMilkProduction(plot.plotNumber, plot.level) : 0;
      if (Math.abs(plot.milkProduction - newMilkProduction) > 0.001) {
        updates.milkProduction = newMilkProduction;
        needsUpdate = true;
      }
      
      // 重新计算升级费用
      const newUpgradeCost = plot.isUnlocked ? getFarmPlotUpgradeCost(plot.plotNumber, plot.level) : 0;
      if (Math.abs(plot.upgradeCost - newUpgradeCost) > 0.001) {
        updates.upgradeCost = newUpgradeCost;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await queryInterface.sequelize.query(
          `UPDATE farm_plots SET 
             ${Object.keys(updates).map(key => `${key} = :${key}`).join(', ')}
           WHERE id = :id`,
          {
            replacements: { ...updates, id: plot.id },
            type: Sequelize.QueryTypes.UPDATE
          }
        );
        
        updatedCount++;
        console.log(`更新农场区块 ${plot.plotNumber} (钱包ID: ${plot.walletId}, 等级: ${plot.level}): ${Object.keys(updates).join(', ')}`);
      }
    }
    
    console.log(`农场区块新配置更新完成，共更新 ${updatedCount} 个记录`);
  },

  down: async (queryInterface, Sequelize) => {
    console.log('回滚农场区块配置数据...');
    console.log('注意：此迁移的回滚将使用旧的公式重新计算数据');
    
    // 旧的计算公式
    function calculateOldBaseProduction(level, plotNumber) {
      return Math.pow(2.0, plotNumber - 1) * Math.pow(1.5, level - 1);
    }
    
    function calculateOldProductionSpeed(level) {
      return 5 / Math.pow(1.05, level - 1);
    }
    
    function calculateOldUpgradeCost(level) {
      return 200 * Math.pow(1.5, level - 1);
    }
    
    function calculateOldUnlockCost(plotNumber) {
      return plotNumber === 1 ? 0 : 2000 * Math.pow(2, plotNumber - 2);
    }
    
    // 获取所有农场区块数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    // 使用旧公式回滚数据
    for (const plot of farmPlots) {
      const oldMilkProduction = plot.isUnlocked ? calculateOldBaseProduction(plot.level, plot.plotNumber) : 0;
      const oldProductionSpeed = plot.isUnlocked ? calculateOldProductionSpeed(plot.level) : 5;
      const oldUpgradeCost = plot.isUnlocked ? calculateOldUpgradeCost(plot.level) : 0;
      const oldUnlockCost = calculateOldUnlockCost(plot.plotNumber);
      const oldBarnCount = plot.isUnlocked ? plot.level : 0;
      
      await queryInterface.sequelize.query(
        `UPDATE farm_plots SET 
           barnCount = :barnCount,
           milkProduction = :milkProduction,
           productionSpeed = :productionSpeed,
           unlockCost = :unlockCost,
           upgradeCost = :upgradeCost
         WHERE id = :id`,
        {
          replacements: {
            barnCount: oldBarnCount,
            milkProduction: oldMilkProduction,
            productionSpeed: oldProductionSpeed,
            unlockCost: oldUnlockCost,
            upgradeCost: oldUpgradeCost,
            id: plot.id
          },
          type: Sequelize.QueryTypes.UPDATE
        }
      );
    }
    
    console.log('农场区块配置数据回滚完成');
  }
};
