const { Sequelize } = require('sequelize');

// 数据库连接配置
const sequelize = new Sequelize(
  'wolf',
  'wolf',
  '00321zixunadmin',
  {
    host: '127.0.0.1',
    dialect: 'mysql',
    port: 3669,
    logging: console.log,
  }
);

async function checkTelegramIdConflict() {
  try {
    console.log('🔍 检查 telegramId 冲突...\n');

    // 测试钱包地址
    const testWalletAddress = '******************************************';
    const testTelegramId = `web3_${testWalletAddress.toLowerCase()}`;
    
    console.log(`测试钱包地址: ${testWalletAddress}`);
    console.log(`生成的 telegramId: ${testTelegramId}\n`);

    // 查询是否已存在相同的 telegramId
    const [existingUsers] = await sequelize.query(
      'SELECT id, username, telegramId, createdAt FROM users WHERE telegramId = ?',
      { replacements: [testTelegramId] }
    );

    if (existingUsers.length > 0) {
      console.log('❌ 发现 telegramId 冲突！');
      console.log('已存在的用户:');
      console.table(existingUsers);
    } else {
      console.log('✅ 没有发现 telegramId 冲突');
    }

    // 查询所有以 web3_ 开头的 telegramId
    const [web3Users] = await sequelize.query(
      'SELECT id, username, telegramId, createdAt FROM users WHERE telegramId LIKE "web3_%" ORDER BY createdAt DESC LIMIT 10'
    );

    if (web3Users.length > 0) {
      console.log('\n📋 最近的 Web3 用户:');
      console.table(web3Users);
    }

    // 检查 telegramId 字段的约束
    const [constraints] = await sequelize.query(`
      SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
      FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'users' 
      AND COLUMN_NAME = 'telegramId'
    `);

    console.log('\n🔧 telegramId 字段约束:');
    console.table(constraints);

    // 检查索引
    const [indexes] = await sequelize.query('SHOW INDEX FROM users WHERE Column_name = "telegramId"');
    
    console.log('\n📊 telegramId 字段索引:');
    console.table(indexes);

    await sequelize.close();
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

checkTelegramIdConflict();
