{"productNotFound": "商品が見つかりません", "purchaseSuccess": "購入が正常に完了しました", "paymentFailed": "支払いに失敗しました", "dailyTypeLimitReached": "{productType}商品の1日の購入制限に達しました。1日に1つの{productType}商品のみ購入できます。", "dailyLimitReached": "商品の1日の購入制限に達しました：{productName}", "accountLimitReached": "商品のアカウント購入制限に達しました：{productName}", "vipAlreadyActive": "VIPメンバーシップは既にアクティブです", "priceNotAvailable": "商品の選択された支払い方法で価格が利用できません：{productName}", "walletIdRequired": "ウォレットIDが必要です", "missingRequiredParameters": "必要なパラメータが不足しています", "walletNotFound": "ウォレットが見つかりません", "dappPortalConfigMissing": "DappPortal設定が不足しています", "userWalletAddressNotFound": "ユーザーウォレットアドレスが見つかりません", "failedToCreatePaymentOrder": "支払い注文の作成に失敗しました", "dappPortalUnavailable": "DappPortalサービスが利用できません", "invalidPaymentResponse": "無効な支払いサービス応答", "walletIdAndBoosterIdRequired": "ウォレットIDとブースターIDが必要です"}