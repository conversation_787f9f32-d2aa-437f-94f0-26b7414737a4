import { Test, TestingModule } from '@nestjs/testing';
import { Web3AuthService } from './web3-auth.service';

describe('Web3AuthService', () => {
  let service: Web3AuthService;
  let mockRedisClient: any;

  beforeEach(async () => {
    // 创建Redis客户端模拟
    mockRedisClient = {
      set: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        Web3AuthService,
        {
          provide: 'REDIS_CLIENT',
          useValue: mockRedisClient,
        },
      ],
    }).compile();

    service = module.get<Web3AuthService>(Web3AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateSignMessage', () => {
    it('should generate correct sign message format', () => {
      const nonce = 'test-nonce-123';
      const message = service.generateSignMessage(nonce);

      expect(message).toContain('Welcome to <PERSON>oFun!');
      expect(message).toContain('Please sign to verify your wallet address');
      expect(message).toContain(`Security code: ${nonce}`);
      expect(message).toContain('Timestamp:');
    });

    it('should include timestamp in message', () => {
      const nonce = 'test-nonce';
      const beforeTime = Date.now();
      const message = service.generateSignMessage(nonce);
      const afterTime = Date.now();

      // 提取时间戳
      const timestampMatch = message.match(/Timestamp: (\d+)/);
      expect(timestampMatch).toBeTruthy();
      
      const timestamp = parseInt(timestampMatch![1]);
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(timestamp).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('checkRedisConnection', () => {
    it('should return true when Redis is working', async () => {
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.get.mockResolvedValue('ok');
      mockRedisClient.del.mockResolvedValue(1);

      const result = await service.checkRedisConnection();

      expect(result).toBe(true);
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'web3_auth_health_check',
        'ok',
        'EX',
        10
      );
      expect(mockRedisClient.get).toHaveBeenCalledWith('web3_auth_health_check');
      expect(mockRedisClient.del).toHaveBeenCalledWith('web3_auth_health_check');
    });

    it('should return false when Redis set fails', async () => {
      mockRedisClient.set.mockRejectedValue(new Error('Redis connection failed'));

      const result = await service.checkRedisConnection();

      expect(result).toBe(false);
    });

    it('should return false when Redis get returns wrong value', async () => {
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.get.mockResolvedValue('wrong-value');
      mockRedisClient.del.mockResolvedValue(1);

      const result = await service.checkRedisConnection();

      expect(result).toBe(false);
    });
  });

  describe('getNonce', () => {
    it('should generate nonce and store in Redis', async () => {
      const walletAddress = '******************************************';
      mockRedisClient.set.mockResolvedValue('OK');

      const result = await service.getNonce({ walletAddress });

      expect(result).toHaveProperty('nonce');
      expect(result).toHaveProperty('message');
      expect(typeof result.nonce).toBe('string');
      expect(result.nonce.length).toBeGreaterThan(0);
      
      // 验证Redis存储调用
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        `web3_auth_nonce:${walletAddress.toLowerCase()}`,
        result.nonce,
        'EX',
        300
      );

      // 验证消息格式
      expect(result.message).toContain('Welcome to MooFun!');
      expect(result.message).toContain(result.nonce);
    });

    it('should convert wallet address to lowercase for Redis key', async () => {
      const walletAddress = '0X1234567890ABCDEF1234567890ABCDEF12345678';
      mockRedisClient.set.mockResolvedValue('OK');

      await service.getNonce({ walletAddress });

      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'web3_auth_nonce:******************************************',
        expect.any(String),
        'EX',
        300
      );
    });

    it('should throw error when wallet address is invalid', async () => {
      await expect(service.getNonce({ walletAddress: '' }))
        .rejects.toThrow('Invalid wallet address format');

      await expect(service.getNonce({ walletAddress: null as any }))
        .rejects.toThrow('Invalid wallet address format');
    });

    it('should throw error when Redis fails', async () => {
      const walletAddress = '******************************************';
      mockRedisClient.set.mockRejectedValue(new Error('Redis error'));

      await expect(service.getNonce({ walletAddress }))
        .rejects.toThrow('Failed to store nonce in Redis');
    });

    it('should generate different nonces for multiple calls', async () => {
      const walletAddress = '******************************************';
      mockRedisClient.set.mockResolvedValue('OK');

      const result1 = await service.getNonce({ walletAddress });
      const result2 = await service.getNonce({ walletAddress });

      expect(result1.nonce).not.toBe(result2.nonce);
    });

    it('should handle different wallet addresses', async () => {
      const wallet1 = '******************************************';
      const wallet2 = '******************************************';
      mockRedisClient.set.mockResolvedValue('OK');

      const result1 = await service.getNonce({ walletAddress: wallet1 });
      const result2 = await service.getNonce({ walletAddress: wallet2 });

      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'web3_auth_nonce:******************************************',
        result1.nonce,
        'EX',
        300
      );
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'web3_auth_nonce:******************************************',
        result2.nonce,
        'EX',
        300
      );
    });
  });
});
