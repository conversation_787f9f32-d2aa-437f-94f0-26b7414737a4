// Web3Auth性能测试脚本
const axios = require('axios');

async function performanceTest() {
  console.log('🚀 Web3Auth性能测试开始...\n');
  
  const baseURL = 'http://localhost:3001/api/web3-auth';
  const testWallet = '******************************************';
  
  // 测试配置
  const testConfigs = [
    { name: '单次请求', concurrent: 1, requests: 1 },
    { name: '顺序请求', concurrent: 1, requests: 10 },
    { name: '并发请求', concurrent: 5, requests: 25 },
    { name: '高并发请求', concurrent: 10, requests: 50 },
  ];
  
  for (const config of testConfigs) {
    console.log(`\n📊 测试: ${config.name}`);
    console.log(`   并发数: ${config.concurrent}, 总请求数: ${config.requests}`);
    console.log('-'.repeat(50));
    
    const results = await runPerformanceTest(baseURL, testWallet, config);
    
    console.log(`✅ 测试完成:`);
    console.log(`   总耗时: ${results.totalTime}ms`);
    console.log(`   平均响应时间: ${results.avgResponseTime}ms`);
    console.log(`   最快响应: ${results.minResponseTime}ms`);
    console.log(`   最慢响应: ${results.maxResponseTime}ms`);
    console.log(`   成功率: ${results.successRate}%`);
    console.log(`   QPS: ${results.qps.toFixed(2)}`);
    
    if (results.errors.length > 0) {
      console.log(`   错误数: ${results.errors.length}`);
      console.log(`   错误类型: ${[...new Set(results.errors.map(e => e.type))].join(', ')}`);
    }
  }
  
  console.log('\n🎉 性能测试完成！');
}

async function runPerformanceTest(baseURL, testWallet, config) {
  const { concurrent, requests } = config;
  const results = {
    totalTime: 0,
    responseTimes: [],
    errors: [],
    successCount: 0,
  };
  
  const startTime = Date.now();
  
  // 创建请求批次
  const batches = [];
  for (let i = 0; i < requests; i += concurrent) {
    const batchSize = Math.min(concurrent, requests - i);
    const batch = Array(batchSize).fill().map(() => makeRequest(baseURL, testWallet));
    batches.push(batch);
  }
  
  // 执行批次
  for (const batch of batches) {
    const batchResults = await Promise.allSettled(batch);
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.responseTimes.push(result.value.responseTime);
        results.successCount++;
      } else {
        results.errors.push({
          type: result.reason.code || result.reason.message || 'unknown',
          message: result.reason.message,
        });
      }
    });
  }
  
  results.totalTime = Date.now() - startTime;
  
  // 计算统计数据
  const avgResponseTime = results.responseTimes.length > 0 
    ? results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length 
    : 0;
  
  const minResponseTime = results.responseTimes.length > 0 
    ? Math.min(...results.responseTimes) 
    : 0;
    
  const maxResponseTime = results.responseTimes.length > 0 
    ? Math.max(...results.responseTimes) 
    : 0;
  
  const successRate = (results.successCount / requests) * 100;
  const qps = results.successCount / (results.totalTime / 1000);
  
  return {
    totalTime: results.totalTime,
    avgResponseTime: Math.round(avgResponseTime),
    minResponseTime,
    maxResponseTime,
    successRate: Math.round(successRate * 100) / 100,
    qps,
    errors: results.errors,
  };
}

async function makeRequest(baseURL, testWallet) {
  const startTime = Date.now();
  
  try {
    const response = await axios.post(`${baseURL}/nonce`, {
      walletAddress: testWallet
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'en'
      },
      timeout: 5000 // 5秒超时
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.data.ok && response.data.data.nonce) {
      return { responseTime, success: true };
    } else {
      throw new Error('Invalid response format');
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    error.responseTime = responseTime;
    throw error;
  }
}

// 内存使用监控
function getMemoryUsage() {
  const used = process.memoryUsage();
  return {
    rss: Math.round(used.rss / 1024 / 1024 * 100) / 100,
    heapTotal: Math.round(used.heapTotal / 1024 / 1024 * 100) / 100,
    heapUsed: Math.round(used.heapUsed / 1024 / 1024 * 100) / 100,
    external: Math.round(used.external / 1024 / 1024 * 100) / 100,
  };
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('📈 系统信息:');
  console.log(`   Node.js版本: ${process.version}`);
  console.log(`   平台: ${process.platform}`);
  console.log(`   内存使用: ${JSON.stringify(getMemoryUsage())} MB`);
  
  performanceTest().catch(console.error);
}

module.exports = { performanceTest, runPerformanceTest };
