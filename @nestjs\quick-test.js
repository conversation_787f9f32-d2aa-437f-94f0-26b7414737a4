// 快速测试Web3Auth接口
const axios = require('axios');

async function quickTest() {
  console.log('🚀 快速测试Web3Auth接口...\n');
  
  try {
    // 1. 健康检查
    console.log('1. 健康检查...');
    const healthResponse = await axios.get('http://localhost:3001/api/web3-auth/health');
    console.log('健康检查响应:', JSON.stringify(healthResponse.data, null, 2));
    
    // 2. 正常nonce请求
    console.log('\n2. 正常nonce请求...');
    const nonceResponse = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
      walletAddress: '******************************************'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh'
      }
    });
    console.log('Nonce响应:', JSON.stringify(nonceResponse.data, null, 2));
    
    // 3. 验证错误请求
    console.log('\n3. 验证错误请求...');
    try {
      await axios.post('http://localhost:3001/api/web3-auth/nonce', {
        // 缺少walletAddress
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': 'zh'
        }
      });
      console.log('❌ 应该返回验证错误');
    } catch (error) {
      console.log('验证错误响应:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.log('\n✅ 快速测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

quickTest();
