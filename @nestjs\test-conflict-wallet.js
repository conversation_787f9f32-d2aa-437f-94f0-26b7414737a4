const axios = require('axios');
const { ethers } = require('ethers');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

async function testConflictWallet() {
  try {
    console.log('🧪 测试有冲突的钱包地址...\n');

    // 使用已知存在telegramId冲突的钱包地址
    const conflictWalletAddress = '******************************************';
    
    console.log(`冲突钱包地址: ${conflictWalletAddress}`);
    console.log('这个地址对应的telegramId在数据库中已经存在\n');

    // 步骤1: 获取nonce
    console.log('1. 获取nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: conflictWalletAddress
    });

    if (!nonceResponse.data.ok) {
      throw new Error(`获取nonce失败: ${nonceResponse.data.message}`);
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取nonce成功: ${nonce.substring(0, 8)}...`);
    console.log(`消息: ${message.substring(0, 50)}...\n`);

    // 步骤2: 生成真实的签名
    console.log('2. 生成真实签名...');
    
    // 使用测试私钥生成签名
    const testPrivateKey = process.env.TEST_ETH_PRIVATE_KEY || '0x740fe33880d587c4f2a2f1c0190f10ebe0a74afd6073a08da8d78c3c8a7cd39d';
    const wallet = new ethers.Wallet(testPrivateKey);
    const signature = await wallet.signMessage(message);
    
    console.log(`✅ 签名生成成功: ${signature.substring(0, 20)}...`);
    console.log(`钱包地址: ${wallet.address}\n`);

    // 步骤3: 测试登录
    console.log('3. 测试登录...');
    
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: wallet.address, // 使用实际的钱包地址
      signature: signature,
      message: message,
      referralCode: undefined
    });

    if (loginResponse.data.ok) {
      console.log('✅ 登录成功！');
      console.log('响应数据:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
    } else {
      console.log('❌ 登录失败:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testConflictWallet();
