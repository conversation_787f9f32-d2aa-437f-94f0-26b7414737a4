#!/usr/bin/env node

/**
 * Web3 登录测试
 * 使用生成的签名测试登录接口
 */

const axios = require('axios');
const { ethers } = require('ethers');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

// 创建一个随机钱包
function createRandomWallet() {
  return ethers.Wallet.createRandom();
}

// 主测试函数
async function testLoginWithSignature() {
  console.log('🔐 Web3 登录测试\n');

  try {
    // 创建钱包
    const wallet = createRandomWallet();
    console.log(`钱包地址: ${wallet.address}`);
    console.log(`私钥: ${wallet.privateKey}`);
    console.log('');

    // 获取 nonce
    console.log('1. 获取 nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: wallet.address
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    if (!nonceResponse.data.ok) {
      console.log('❌ 获取 nonce 失败');
      console.log(nonceResponse.data);
      return;
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取 nonce 成功: ${nonce}`);
    console.log(`消息: ${message.substring(0, 50)}...`);
    console.log('');

    // 签名消息
    console.log('2. 签名消息...');
    const signature = await wallet.signMessage(message);
    console.log(`✅ 签名成功: ${signature.substring(0, 30)}...`);
    console.log('');

    // 验证签名（本地）
    console.log('3. 本地验证签名...');
    const recoveredAddress = ethers.verifyMessage(message, signature);
    console.log(`恢复的地址: ${recoveredAddress}`);
    console.log(`原始地址: ${wallet.address}`);
    console.log(`验证结果: ${recoveredAddress.toLowerCase() === wallet.address.toLowerCase() ? '✅ 成功' : '❌ 失败'}`);
    console.log('');

    // 登录
    console.log('4. 登录...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: wallet.address,
      signature: signature,
      message: message
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    console.log('登录响应:');
    console.log(JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.ok) {
      console.log('✅ 登录成功！');
      console.log(`用户ID: ${loginResponse.data.data.user.id}`);
      console.log(`用户名: ${loginResponse.data.data.user.username}`);
      console.log(`钱包地址: ${loginResponse.data.data.user.walletAddress}`);
      console.log(`Token: ${loginResponse.data.data.token.substring(0, 30)}...`);
    } else {
      console.log('❌ 登录失败');
      console.log(`错误消息: ${loginResponse.data.message}`);
      console.log(`错误详情: ${loginResponse.data.error}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 测试开发环境特定钱包
async function testDevWallet() {
  console.log('\n🔧 测试开发环境特定钱包\n');

  try {
    // 使用开发环境的测试钱包地址
    const testWalletAddress = '******************************************';
    console.log(`测试钱包地址: ${testWalletAddress}`);
    console.log('');

    // 获取 nonce
    console.log('1. 获取 nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: testWalletAddress
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    if (!nonceResponse.data.ok) {
      console.log('❌ 获取 nonce 失败');
      console.log(nonceResponse.data);
      return;
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取 nonce 成功: ${nonce}`);
    console.log('');

    // 使用任意签名（开发环境应该跳过验证）
    const testSignature = '0x' + '1'.repeat(130);
    
    // 登录
    console.log('2. 登录...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: testWalletAddress,
      signature: testSignature,
      message: message
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      }
    });

    console.log('登录响应:');
    console.log(JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.ok) {
      console.log('✅ 开发环境登录成功！');
    } else {
      console.log('❌ 开发环境登录失败');
      console.log(`错误消息: ${loginResponse.data.message}`);
      console.log(`错误详情: ${loginResponse.data.error}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
async function runTests() {
  // 测试真实钱包登录
  await testLoginWithSignature();
  
  // 测试开发环境特定钱包
  await testDevWallet();
}

runTests().catch(console.error);
