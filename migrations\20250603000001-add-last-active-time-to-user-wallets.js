'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn('user_wallets', 'lastActiveTime', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '最后活跃时间，用于计算离线奖励'
      });
      console.log('成功添加lastActiveTime字段到user_wallets表');
    } catch (error) {
      console.error('添加lastActiveTime字段失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('user_wallets', 'lastActiveTime');
      console.log('成功从user_wallets表删除lastActiveTime字段');
    } catch (error) {
      console.error('删除lastActiveTime字段失败:', error);
      throw error;
    }
  }
}; 