#!/usr/bin/env node

/**
 * 签名验证测试
 * 测试 ethers.js 签名验证功能
 */

const { ethers } = require('ethers');

// 创建一个随机钱包
function createRandomWallet() {
  return ethers.Wallet.createRandom();
}

// 签名消息
async function signMessage(wallet, message) {
  return wallet.signMessage(message);
}

// 验证签名
function verifySignature(message, signature) {
  try {
    const recoveredAddress = ethers.verifyMessage(message, signature);
    return recoveredAddress;
  } catch (error) {
    console.error('验证签名失败:', error);
    return null;
  }
}

// 主测试函数
async function testSignatureVerification() {
  console.log('🔐 签名验证测试\n');

  // 创建钱包
  const wallet = createRandomWallet();
  console.log(`钱包地址: ${wallet.address}`);
  console.log(`私钥: ${wallet.privateKey}`);
  console.log('');

  // 测试消息
  const message = 'Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: 12345678\nTimestamp: ' + Date.now();
  console.log(`消息: ${message.substring(0, 50)}...`);
  console.log('');

  // 签名消息
  const signature = await signMessage(wallet, message);
  console.log(`签名: ${signature.substring(0, 30)}...`);
  console.log('');

  // 验证签名
  const recoveredAddress = verifySignature(message, signature);
  console.log(`恢复的地址: ${recoveredAddress}`);
  console.log(`原始地址: ${wallet.address}`);
  console.log(`验证结果: ${recoveredAddress.toLowerCase() === wallet.address.toLowerCase() ? '✅ 成功' : '❌ 失败'}`);
  console.log('');

  // 测试错误的签名
  console.log('测试错误的签名:');
  const wrongSignature = '0x' + '1'.repeat(130); // 创建一个错误的签名
  try {
    const wrongRecoveredAddress = verifySignature(message, wrongSignature);
    console.log(`错误签名恢复的地址: ${wrongRecoveredAddress || 'null'}`);
    console.log(`验证结果: ${wrongRecoveredAddress && wrongRecoveredAddress.toLowerCase() === wallet.address.toLowerCase() ? '❌ 错误地通过了验证' : '✅ 正确地验证失败'}`);
  } catch (error) {
    console.log(`✅ 正确地验证失败: ${error.message}`);
  }
  console.log('');

  // 测试错误的消息
  console.log('测试错误的消息:');
  const wrongMessage = message + ' (modified)';
  const wrongMessageRecoveredAddress = verifySignature(wrongMessage, signature);
  console.log(`错误消息恢复的地址: ${wrongMessageRecoveredAddress || 'null'}`);
  console.log(`验证结果: ${wrongMessageRecoveredAddress && wrongMessageRecoveredAddress.toLowerCase() === wallet.address.toLowerCase() ? '❌ 错误地通过了验证' : '✅ 正确地验证失败'}`);
  console.log('');

  // 输出可用于测试的信息
  console.log('🔧 测试信息:');
  console.log(`钱包地址: ${wallet.address}`);
  console.log(`消息: ${message}`);
  console.log(`签名: ${signature}`);
  console.log('');
  console.log('可以使用这些信息在 Web3 登录接口中进行测试');
}

// 运行测试
testSignatureVerification().catch(console.error);
