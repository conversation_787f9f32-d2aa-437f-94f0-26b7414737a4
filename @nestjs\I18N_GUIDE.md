# NestJS 多语言国际化 (I18n) 指南

## 概述

本项目已集成 `nestjs-i18n` 库，提供完整的多语言支持功能，替换了原有基于 `node-polyglot` 的翻译系统。

## 支持的语言

- 🇺🇸 英语 (en) - 默认语言
- 🇨🇳 中文简体 (zh)
- 🇹🇼 中文繁体 (zh-tw)
- 🇯🇵 日语 (ja)

## 语言检测逻辑

系统使用简化的语言检测逻辑：

1. **Accept-Language 请求头** - 唯一的语言检测方式
2. **默认语言** - 如果没有 Accept-Language 头或不支持的语言，使用 `en`

系统会解析 Accept-Language 头中的语言列表，按质量值(q值)排序，选择第一个支持的语言。支持智能匹配，如 `zh-CN` 会匹配到 `zh`。

## 翻译文件结构

```
src/i18n/
├── en/
│   ├── common.json      # 通用翻译
│   ├── errors.json      # 错误消息
│   ├── tasks.json       # 任务相关
│   ├── iap.json         # 内购相关
│   └── validation.json  # 验证消息
├── zh/
│   ├── common.json
│   ├── errors.json
│   ├── tasks.json
│   ├── iap.json
│   └── validation.json
├── zh-tw/
│   └── ... (同上)
└── ja/
    └── ... (同上)
```

## 使用方法

### 1. 在控制器中使用

```typescript
import { Controller, Get } from '@nestjs/common';
import { I18nLang, I18nTranslate } from '../common/decorators';
import { CustomI18nService } from '../i18n/i18n.service';

@Controller('example')
export class ExampleController {
  constructor(private readonly i18nService: CustomI18nService) {}

  @Get()
  example(
    @I18nLang() language: string,
    @I18nTranslate() t: (key: string, options?: any) => string,
  ) {
    return {
      message: t('common.success'),
      language,
      customMessage: this.i18nService.translate('errors.unauthorized', {}, language),
    };
  }
}
```

### 2. 在服务中使用

```typescript
import { Injectable } from '@nestjs/common';
import { CustomI18nService } from '../i18n/i18n.service';

@Injectable()
export class ExampleService {
  constructor(private readonly i18nService: CustomI18nService) {}

  processError(errorMessage: string, language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    return this.i18nService.processErrorMessage(errorMessage, lang);
  }

  translateWithVariables(key: string, variables: any, language: string) {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    return this.i18nService.translate(key, variables, lang);
  }
}
```

### 3. 兼容原有系统

```typescript
// 原有的 tFromRequest 函数仍然可用
const translatedMessage = this.i18nService.tFromRequest(req, 'common.success');

// 原有的 TranslationService 已更新为使用新的 i18n 系统
const message = this.translationService.translate('common.error', language);
```

## API 测试

### 测试不同语言

```bash
# 英语
curl -H "Accept-Language: en-US,en;q=0.9" http://localhost:3001/api/health

# 中文简体
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/health

# 中文繁体
curl -H "Accept-Language: zh-TW,zh-tw;q=0.9,zh;q=0.8,en;q=0.7" http://localhost:3001/api/health

# 日语
curl -H "Accept-Language: ja-JP,ja;q=0.9,en;q=0.8" http://localhost:3001/api/health

# 多语言优先级测试
curl -H "Accept-Language: fr;q=0.9,zh;q=0.8,en;q=0.7" http://localhost:3001/api/health
```

### 专用测试接口

```bash
# I18n 功能测试
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/i18n-test
```

### 自动化测试

```bash
# 运行 I18n 测试脚本
node test-i18n.js
```

## 翻译键命名规范

### 通用翻译 (common.json)
- `common.success` - 成功
- `common.error` - 错误
- `common.not_found` - 未找到

### 错误消息 (errors.json)
- `errors.unauthorized` - 未授权
- `errors.walletNotFound` - 钱包未找到
- `errors.insufficientBalance` - 余额不足

### 任务相关 (tasks.json)
- `tasks.dailySignin` - 每日签到
- `tasks.inviteFriends` - 邀请朋友

### 验证消息 (validation.json)
- `validation.required` - 必填字段
- `validation.email` - 邮箱格式

## 变量插值

支持在翻译文本中使用变量：

```json
{
  "notEnoughChests": "宝箱数量不足。需要：{required}，可用：{available}"
}
```

```typescript
const message = this.i18nService.translate('errors.notEnoughChests', {
  required: 5,
  available: 2
}, 'zh');
// 输出: "宝箱数量不足。需要：5，可用：2"
```

## 错误处理

系统会自动翻译常见的HTTP错误：

- 401 Unauthorized → `common.unauthorized`
- 403 Forbidden → `common.forbidden`
- 404 Not Found → `common.not_found`
- 400 Bad Request → `common.invalid_request`
- 500 Internal Server Error → `common.server_error`

## 向后兼容性

- ✅ 保持与现有 Express 应用相同的语言检测逻辑
- ✅ 支持原有的 `tFromRequest` 函数
- ✅ 兼容现有的 `@Language()` 装饰器
- ✅ 维持相同的API响应格式
- ✅ 支持现有的错误消息处理

## 开发指南

### 添加新的翻译

1. 在对应语言目录下的JSON文件中添加翻译键值对
2. 确保所有支持的语言都有对应的翻译
3. 使用有意义的键名，遵循命名规范

### 添加新语言

1. 在 `src/i18n/` 下创建新的语言目录
2. 复制现有语言的JSON文件并翻译
3. 更新 `SUPPORTED_LANGUAGES` 数组
4. 更新配置文件中的支持语言列表

### 调试翻译

开发环境下，翻译系统会：
- 监听翻译文件变化
- 在缺失翻译键时抛出错误
- 输出详细的日志信息

## 性能优化

- 翻译文件在应用启动时加载到内存
- 支持翻译缓存
- 开发环境支持热重载
- 生产环境优化性能
