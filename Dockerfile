# 使用官方 Node.js 镜像
FROM node:22-alpine


ENV TIME_ZONE=Asia/Shanghai

RUN \
  mkdir -p /usr/src/app \
  && apk add --no-cache tzdata \
  && echo "${TIME_ZONE}" > /etc/timezone \ 
  && ln -sf /usr/share/zoneinfo/${TIME_ZONE} /etc/localtime 

# 创建工作目录
WORKDIR /app
# 复制 package.json 和 package-lock.json
COPY package*.json ./

COPY .npmrc ./

# 安装项目依赖
RUN npm install

# 复制项目文件到容器中
COPY . .

COPY .env_prod .env

RUN npm run build

ENV NODE_ENV=production

# 容器启动时的命令
CMD ["node", "dist/app.js"]

# 暴露端口
EXPOSE 3456