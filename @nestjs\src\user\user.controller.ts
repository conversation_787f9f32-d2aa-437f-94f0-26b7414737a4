import { Controller, Get, UseGuards, HttpStatus, HttpException } from '@nestjs/common';
import { UserService } from './user.service';
import { WalletAuthGuard } from '../auth/guards/wallet-auth.guard';
import { User } from '../common/decorators/user.decorator';
import { RequestUser } from '../common/services/request-context.service';
import { CustomI18nService, SupportedLanguage } from '../i18n/i18n.service';
import { UserInfoResponseDto } from './dto/user-info.dto';
import { I18nLang } from '../i18n/decorators/i18n.decorator';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly i18nService: CustomI18nService,
  ) {}

  /**
   * 获取当前用户信息
   * 路径: /api/user/me
   * 方法: GET
   * 需要认证: 是 (WalletAuth)
   */
  @Get('me')
  @UseGuards(WalletAuthGuard)
  async getUserMe(
    @User() user: RequestUser,
    @I18nLang() language: string,
  ): Promise<UserInfoResponseDto[]> {
    const { userId, walletId } = user;

    if (!walletId) {
      const lang = language as SupportedLanguage || 'en';
      const errorMessage = this.i18nService.translate('errors.missingWalletId', {}, lang);

      // 抛出HTTP异常，状态码400，保持与原版本一致
      throw new HttpException({
        ok: false,
        message: errorMessage,
      }, HttpStatus.BAD_REQUEST);
    }

    try {
      const userInfo = await this.userService.getUserInfo(userId, walletId, language);

      // 直接返回业务数据，让全局拦截器包装为 {ok: true, data: userInfo}
      return userInfo;
    } catch (error: any) {
      const lang = language as SupportedLanguage || 'en';
      const errorMessage = this.i18nService.translate('errors.userInfoFailed', {}, lang);

      // 抛出400状态码，保持与原版本一致
      throw new HttpException({
        ok: false,
        message: errorMessage,
        error: error.message,
      }, HttpStatus.BAD_REQUEST);
    }
  }
}
