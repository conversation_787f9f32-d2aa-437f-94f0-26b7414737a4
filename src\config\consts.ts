// invitationRewards.ts (硬编码示例)
import dotenv from "dotenv";
dotenv.config();

export const invitationRewards = [
  { invitesNeeded: 2, dailyChests: 1 },
  { invitesNeeded: 5, dailyChests: 2 },
  { invitesNeeded: 10, dailyChests: 3 },
  { invitesNeeded: 20, dailyChests: 4 },
  { invitesNeeded: 30, dailyChests: 5 },
];

// 游戏相关常量配置
export const gameConfig = {
  ticketCost: 1, // 每张门票的成本(USD)
  minBetAmount: 1, // 最小下注金额(USD)
  maxBetAmount: 1000, // 最大下注金额(USD)
  roomTypes: [
    { id: 1, name: "初级房", minBet: 1, maxBet: 100, ticketRequired: 1 },
    { id: 2, name: "中级房", minBet: 50, maxBet: 500, ticketRequired: 2 },
    { id: 3, name: "高级房", minBet: 200, maxBet: 1000, ticketRequired: 3 }
  ],
  roundDuration: 180, // 每轮游戏持续时间(秒)
  betWindowDuration: 120 // 下注窗口时间(秒)
};

// 游戏预约配置
export const GAME_CONFIG = {
  TICKET_COST: 1, // 每次预约需要的门票数量
  USD_COST: 100, // 每次预约需要的 USD 数量
  MAX_PARTICIPANTS: Number(process.env.ROOM_CAPACITY!)! // 每个房间的最大参与人数
};

// 门票返利配置
export const TICKET_REBATE_CONFIG = {
  // 总返利比例 (30%)
  TOTAL_REBATE_PERCENTAGE: 0.3,
  // 每个级别的返利比例
  LEVEL_PERCENTAGES: {
    1: 0.10, // 10%
    2: 0.10, // 10%
    3: 0.05, // 5%
    4: 0.03, // 3%
    5: 0.02, // 2%
  },
  // 解锁全部返利所需的每日最低游戏轮数
  MIN_DAILY_ROUNDS: 3
};
