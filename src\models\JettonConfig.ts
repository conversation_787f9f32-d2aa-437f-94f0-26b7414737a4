// src/models/JettonConfig.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface JettonConfigAttributes {
  id: number;
  name: string;
  address: string;
  decimals: number;
  isActive: boolean;
  description: string;
  createdAt?: Date;
  updatedAt?: Date;
}

type JettonConfigCreationAttributes = Optional<JettonConfigAttributes, "id" | "description" | "isActive">;

export class JettonConfig
  extends Model<JettonConfigAttributes, JettonConfigCreationAttributes>
  implements JettonConfigAttributes
{
  public id!: number;
  public name!: string;
  public address!: string;
  public decimals!: number;
  public isActive!: boolean;
  public description!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

JettonConfig.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    address: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    decimals: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 6,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: "jetton_configs",
    timestamps: true,
  }
);