#!/usr/bin/env node

/**
 * 测试 Web3 签名测试接口功能
 * 验证 /api/web3-sign-test/sign 接口是否正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/web3-sign-test';

// 测试用例
const testCases = [
  {
    name: '英语 (en)',
    headers: { 'Accept-Language': 'en-US,en;q=0.9' },
    expectedLang: 'en'
  },
  {
    name: '中文简体 (zh)',
    headers: { 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8' },
    expectedLang: 'zh'
  },
  {
    name: '中文繁体 (zh-tw)',
    headers: { 'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8' },
    expectedLang: 'zh-tw'
  },
  {
    name: '日语 (ja)',
    headers: { 'Accept-Language': 'ja-<PERSON>,ja;q=0.9,en;q=0.8' },
    expectedLang: 'ja'
  }
];

async function testWeb3SignTest() {
  console.log('🔐 测试 Web3 签名测试接口功能...\n');

  // 检查服务器是否运行
  try {
    await axios.get('http://localhost:3001/api/health');
  } catch (error) {
    console.log('❌ 服务器未运行，请先启动应用: npm run start:dev');
    process.exit(1);
  }

  let allTestsPassed = true;

  for (const testCase of testCases) {
    console.log(`📝 测试: ${testCase.name}`);
    console.log('----------------------------------------');

    try {
      // 步骤1: 测试正常签名请求
      console.log('1. 测试正常签名请求...');
      const testMessage = 'Hello, Web3 World! This is a test message for signing.';
      
      const signResponse = await axios.post(`${BASE_URL}/sign`, {
        message: testMessage
      }, {
        headers: testCase.headers
      });

      if (signResponse.data.ok) {
        console.log('✅ 签名成功');
        console.log(`   消息: ${signResponse.data.data.message.substring(0, 50)}...`);
        console.log(`   签名: ${signResponse.data.data.signature.substring(0, 20)}...`);
        console.log(`   地址: ${signResponse.data.data.address}`);
        
        // 验证响应格式
        const { signature, address, message } = signResponse.data.data;
        if (!signature || !address || !message) {
          console.log('❌ 响应格式不正确');
          allTestsPassed = false;
        } else if (signature.length !== 132 || !signature.startsWith('0x')) {
          console.log('❌ 签名格式不正确');
          allTestsPassed = false;
        } else if (!address.startsWith('0x') || address.length !== 42) {
          console.log('❌ 地址格式不正确');
          allTestsPassed = false;
        } else {
          console.log('✅ 响应格式验证通过');
        }
      } else {
        console.log('❌ 签名失败');
        console.log(`   错误消息: ${signResponse.data.message}`);
        // 如果是因为缺少私钥环境变量，这是可以理解的
        if (signResponse.data.error && signResponse.data.error.includes('TEST_ETH_PRIVATE_KEY')) {
          console.log('   (这是因为缺少 TEST_ETH_PRIVATE_KEY 环境变量，这是正常的)');
        } else {
          allTestsPassed = false;
        }
      }

      // 步骤2: 测试参数验证
      console.log('2. 测试参数验证...');
      try {
        await axios.post(`${BASE_URL}/sign`, {
          // 缺少必需的 message 参数
        }, {
          headers: testCase.headers
        });
        console.log('❌ 应该返回参数验证错误');
        allTestsPassed = false;
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 参数验证错误正确返回');
          console.log(`   状态码: ${error.response.status}`);
          if (error.response.data.message) {
            console.log(`   错误消息: ${error.response.data.message}`);
          }
        } else {
          console.log('❌ 参数验证错误处理不正确');
          console.log(`   状态码: ${error.response?.status}`);
          allTestsPassed = false;
        }
      }

      // 步骤3: 测试空消息
      console.log('3. 测试空消息...');
      try {
        await axios.post(`${BASE_URL}/sign`, {
          message: ''
        }, {
          headers: testCase.headers
        });
        console.log('❌ 应该返回参数验证错误');
        allTestsPassed = false;
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 空消息验证错误正确返回');
        } else {
          console.log('❌ 空消息验证错误处理不正确');
          allTestsPassed = false;
        }
      }

      // 步骤4: 测试长消息
      console.log('4. 测试长消息...');
      const longMessage = 'A'.repeat(1000); // 1000字符的长消息
      
      try {
        const longMessageResponse = await axios.post(`${BASE_URL}/sign`, {
          message: longMessage
        }, {
          headers: testCase.headers
        });

        if (longMessageResponse.data.ok) {
          console.log('✅ 长消息签名成功');
          console.log(`   消息长度: ${longMessageResponse.data.data.message.length}`);
        } else {
          console.log('❌ 长消息签名失败');
          if (longMessageResponse.data.error && longMessageResponse.data.error.includes('TEST_ETH_PRIVATE_KEY')) {
            console.log('   (这是因为缺少 TEST_ETH_PRIVATE_KEY 环境变量)');
          } else {
            allTestsPassed = false;
          }
        }
      } catch (error) {
        console.log('❌ 长消息测试失败');
        console.log(`   错误: ${error.message}`);
        allTestsPassed = false;
      }

    } catch (error) {
      console.log(`❌ 测试失败: ${error.message}`);
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   响应: ${JSON.stringify(error.response.data, null, 2)}`);
      }
      allTestsPassed = false;
    }

    console.log('');
  }

  if (allTestsPassed) {
    console.log('🎉 所有 Web3 签名测试通过！');
    console.log('✅ /api/web3-sign-test/sign 接口功能正常');
    console.log('💡 提示: 如果要完整测试签名功能，请设置 TEST_ETH_PRIVATE_KEY 环境变量');
  } else {
    console.log('❌ 部分测试失败，请检查实现');
    process.exit(1);
  }
}

// 运行测试
testWeb3SignTest().catch(error => {
  console.error('测试执行失败:', error.message);
  process.exit(1);
});
