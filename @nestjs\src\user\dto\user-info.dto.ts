/**
 * 用户信息相关的DTO定义
 * 保持与原Express接口完全兼容的数据结构
 */

export class UserReferralDto {
  username?: string;
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  walletAddress?: string;
}

export class UserInfoResponseDto {
  referralCount?: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  walletAddress?: string;
  referral: UserReferralDto;
  gem?: number | string;
  usd?: number;
  ticket?: number;
  free_ticket?: number;
  moof?: number;
  unlockMoof?: number;
  fragment_green?: number;
  fragment_blue?: number;
  fragment_purple?: number;
  fragment_gold?: number;
  code?: string;
  createdAt: Date;
  email?: string;
}

/**
 * /api/user/me 接口的响应格式
 * 保持与原接口完全一致的响应结构
 */
export class UserMeResponseDto {
  ok: boolean;
  data: UserInfoResponseDto[];
  message?: string;
  pagination?: any;
}

/**
 * 错误响应格式
 * 保持与原接口完全一致的错误响应结构
 */
export class UserMeErrorResponseDto {
  ok: boolean;
  message: string;
  error?: any;
}
