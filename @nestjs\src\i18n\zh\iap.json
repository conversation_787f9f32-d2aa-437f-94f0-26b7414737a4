{"productNotFound": "未找到产品", "purchaseSuccess": "购买成功完成", "paymentFailed": "支付失败", "dailyTypeLimitReached": "已达到{productType}产品的每日购买限制。您每天只能购买一个{productType}产品。", "dailyLimitReached": "已达到产品的每日购买限制：{productName}", "accountLimitReached": "已达到产品的账户购买限制：{productName}", "vipAlreadyActive": "VIP会员已激活", "priceNotAvailable": "所选支付方式不可用于产品：{productName}", "walletIdRequired": "需要钱包ID", "missingRequiredParameters": "缺少必需参数", "walletNotFound": "未找到钱包", "dappPortalConfigMissing": "DappPortal配置缺失", "userWalletAddressNotFound": "未找到用户钱包地址", "failedToCreatePaymentOrder": "创建支付订单失败", "dappPortalUnavailable": "DappPortal服务不可用", "invalidPaymentResponse": "支付服务响应无效", "walletIdAndBoosterIdRequired": "需要钱包ID和道具ID"}