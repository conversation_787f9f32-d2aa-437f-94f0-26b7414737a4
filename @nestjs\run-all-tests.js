// 完整的Web3Auth测试套件运行脚本
const { testWeb3AuthNonce } = require('./test-web3-auth');
const { testRedisConnection } = require('./test-redis-connection');
const { testErrorHandling } = require('./test-error-handling');
const { performanceTest } = require('./test-performance');

async function checkServerHealth() {
  const axios = require('axios');
  
  try {
    console.log('🔍 检查服务器状态...');
    
    // 检查主应用健康状态
    await axios.get('http://localhost:3001/health', { timeout: 5000 });
    console.log('✅ 主应用运行正常');
    
    // 检查Web3Auth健康状态
    const healthResponse = await axios.get('http://localhost:3001/api/web3-auth/health', { timeout: 5000 });
    if (healthResponse.data.ok && healthResponse.data.data.redis === 'connected') {
      console.log('✅ Web3Auth模块运行正常');
      console.log('✅ Redis连接正常');
      return true;
    } else {
      console.log('❌ Web3Auth模块或Redis连接异常');
      return false;
    }
  } catch (error) {
    console.log('❌ 服务器未运行或无法访问');
    console.log('请确保NestJS应用正在运行: npm run start:dev');
    return false;
  }
}

async function runAllTests() {
  console.log('🧪 Web3Auth完整测试套件');
  console.log('='.repeat(60));
  console.log('');
  
  // 检查服务器状态
  const isServerHealthy = await checkServerHealth();
  if (!isServerHealthy) {
    console.log('\n❌ 服务器健康检查失败，终止测试');
    process.exit(1);
  }
  
  console.log('\n🚀 开始运行测试套件...\n');
  
  const testResults = {
    basic: false,
    redis: false,
    errorHandling: false,
    performance: false,
  };
  
  try {
    // 1. 基本功能测试
    console.log('📋 1. 基本功能测试');
    console.log('='.repeat(40));
    await testWeb3AuthNonce();
    testResults.basic = true;
    console.log('✅ 基本功能测试通过\n');
  } catch (error) {
    console.log('❌ 基本功能测试失败:', error.message);
    console.log('');
  }
  
  try {
    // 2. Redis连接测试
    console.log('📋 2. Redis连接测试');
    console.log('='.repeat(40));
    await testRedisConnection();
    testResults.redis = true;
    console.log('✅ Redis连接测试通过\n');
  } catch (error) {
    console.log('❌ Redis连接测试失败:', error.message);
    console.log('');
  }
  
  try {
    // 3. 错误处理测试
    console.log('📋 3. 错误处理测试');
    console.log('='.repeat(40));
    await testErrorHandling();
    testResults.errorHandling = true;
    console.log('✅ 错误处理测试通过\n');
  } catch (error) {
    console.log('❌ 错误处理测试失败:', error.message);
    console.log('');
  }
  
  try {
    // 4. 性能测试
    console.log('📋 4. 性能测试');
    console.log('='.repeat(40));
    await performanceTest();
    testResults.performance = true;
    console.log('✅ 性能测试通过\n');
  } catch (error) {
    console.log('❌ 性能测试失败:', error.message);
    console.log('');
  }
  
  // 测试结果总结
  console.log('📊 测试结果总结');
  console.log('='.repeat(60));
  
  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${totalTests - passedTests}`);
  console.log(`通过率: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  console.log('\n详细结果:');
  console.log(`  基本功能测试: ${testResults.basic ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  Redis连接测试: ${testResults.redis ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  错误处理测试: ${testResults.errorHandling ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  性能测试: ${testResults.performance ? '✅ 通过' : '❌ 失败'}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！Web3Auth nonce接口重构成功！');
    console.log('\n📋 重构完成清单:');
    console.log('  ✅ 接口功能完全兼容');
    console.log('  ✅ 错误处理机制完善');
    console.log('  ✅ Redis连接正常工作');
    console.log('  ✅ 多语言支持正常');
    console.log('  ✅ 性能达到预期标准');
    console.log('  ✅ 单元测试覆盖完整');
    
    console.log('\n🚀 可以开始使用重构后的接口！');
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关问题');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('\n❌ 测试套件运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests, checkServerHealth };
