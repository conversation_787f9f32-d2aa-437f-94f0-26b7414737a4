import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { processAutoCollectChests, initializeJackpotPools } from "../services/jackpotChestService";

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[JackpotChestWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[JackpotChestWorker] ${message}`, ...args);
  },
};

console.log('jackpotChestWorker.ts loaded');



// 处理自动领取宝箱
async function handleAutoCollect() {
  try {
    // logger.info('开始处理自动领取宝箱...');
    await processAutoCollectChests();
    // logger.info('自动领取宝箱处理完成');
  } catch (error) {
    logger.error('处理自动领取宝箱失败:', error);
  }
}

// 创建Worker
const worker = new Worker(
  'jackpot-chest-queue',
  async (job: Job) => {
    logger.info(`处理任务: ${job.name}`);
    
    switch (job.name) {
      case 'initialize-jackpot':
        // await initializeJackpot();
        break;
      case 'auto-collect-chests':
        await handleAutoCollect();
        break;
      default:
        logger.info(`未知任务类型: ${job.name}`);
    }
    
    return { success: true };
  },
  {
    connection: redis,
    concurrency: 1,
  }
);

worker.on('completed', (job) => {
  // logger.info(`任务完成: ${job?.name}`);
});

worker.on('failed', (job, error) => {
  logger.error(`任务失败: ${job?.name}`, error);
});

export default worker;

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    console.log('JackpotChestWorker 收到关闭信号，正在清理资源...');
    
    try {
      // 清理资源，例如关闭数据库连接、取消定时器等
      // 关闭 worker 连接
      await worker.close();
      
      console.log('JackpotChestWorker 资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      if (process.send) {
        process.send({ type: 'ready_to_exit' });
      }
      
      // 直接退出，不等待主进程响应
      process.exit(0);
    } catch (err) {
      console.error('JackpotChestWorker 清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on('SIGTERM', async () => {
  console.log('JackpotChestWorker 收到 SIGTERM 信号，准备退出');
  try {
    await worker.close();
  } catch (err) {
    console.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});


// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化 Jackpot 宝箱处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return worker;
}