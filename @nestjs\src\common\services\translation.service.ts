import { Injectable } from '@nestjs/common';
import { CustomI18nService, SupportedLanguage } from '../../i18n/i18n.service';

@Injectable()
export class TranslationService {
  constructor(private readonly i18nService: CustomI18nService) {}

  /**
   * 翻译文本
   * @param key 翻译键
   * @param language 语言代码
   * @param options 插值选项
   * @returns 翻译后的文本
   */
  translate(
    key: string,
    language: string = 'en',
    options?: Record<string, any>
  ): string {
    const lang = this.i18nService.findBestMatchingLanguage(language);
    return this.i18nService.translate(key, options, lang);
  }

  /**
   * 兼容原有的 t 函数
   * @param key 翻译键
   * @param options 插值选项
   * @param lang 指定语言
   * @returns 翻译后的文本
   */
  t(
    key: string,
    options?: Record<string, any>,
    lang?: SupportedLanguage
  ): string {
    return this.i18nService.t(key, options, lang);
  }

  /**
   * 从请求对象获取翻译
   * @param req 请求对象
   * @param key 翻译键
   * @param options 插值选项
   * @returns 翻译后的文本
   */
  tFromRequest(
    req: any,
    key: string,
    options?: Record<string, any>
  ): string {
    return this.i18nService.tFromRequest(req, key, options);
  }

  /**
   * 格式化验证错误
   * @param errors 错误数组
   * @param lang 语言
   * @returns 格式化后的错误
   */
  formatValidationErrors(
    errors: any[],
    lang: SupportedLanguage = 'en'
  ): Array<{ field: string; message: string }> {
    return this.i18nService.formatValidationErrors(errors, lang);
  }

  /**
   * 处理错误消息
   * @param errorMessage 错误消息
   * @param lang 语言
   * @returns 翻译后的错误消息
   */
  processErrorMessage(errorMessage: string, lang: SupportedLanguage = 'en'): string {
    return this.i18nService.processErrorMessage(errorMessage, lang);
  }

  /**
   * 获取当前语言
   * @returns 当前语言
   */
  getCurrentLanguage(): SupportedLanguage {
    return this.i18nService.getCurrentLanguage();
  }

  /**
   * 查找最匹配的语言
   * @param lang 语言代码
   * @returns 匹配的语言
   */
  findBestMatchingLanguage(lang: string): SupportedLanguage {
    return this.i18nService.findBestMatchingLanguage(lang);
  }

  /**
   * 向后兼容：添加翻译（现在通过文件管理）
   * @deprecated 请使用翻译文件管理翻译内容
   */
  addTranslations(language: string, translations: Record<string, string>) {
    console.warn('addTranslations is deprecated. Please use translation files instead.');
    // 这个方法保留用于向后兼容，但建议使用翻译文件
  }
}
