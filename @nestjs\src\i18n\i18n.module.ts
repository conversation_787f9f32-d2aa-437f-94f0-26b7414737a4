import { Module, Global } from '@nestjs/common';
import {
  I18nModule as NestI18nModule,
  AcceptLanguageResolver,
  I18nJsonLoader
} from 'nestjs-i18n';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as path from 'path';
import { CustomI18nService } from './i18n.service';

@Global()
@Module({
  imports: [
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const i18nConfig = configService.get('i18n') || {};
        const translationPath = path.join(__dirname, './');
        console.log('🔍 I18n translation path:', translationPath);
        console.log('🔍 __dirname:', __dirname);

        return {
          fallbackLanguage: i18nConfig.fallbackLanguage || 'en',
          loader: I18nJsonLoader,
          loaderOptions: {
            path: translationPath,
            watch: process.env.NODE_ENV === 'development',
          },
          formatter: i18nConfig.formatter || 'icu',
          logging: process.env.NODE_ENV === 'development',
          throwOnMissingKey: process.env.NODE_ENV === 'development',
          typesOutputPath: i18nConfig.typesOutputPath || path.join(__dirname, '../generated/i18n.generated.ts'),
        };
      },
      resolvers: [
        // 只使用 Accept-Language 头进行语言检测，符合简化的语言检测要求
        AcceptLanguageResolver,
      ],
      inject: [ConfigService],
    }),
  ],
  providers: [
    CustomI18nService,
  ],
  exports: [
    NestI18nModule,
    CustomI18nService,
  ],
})
export class I18nModule {}
