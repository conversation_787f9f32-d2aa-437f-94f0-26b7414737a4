@echo off
echo 开始安装 NestJS 项目依赖...

REM 检查是否在正确的目录
if not exist "package.json" (
    echo 错误: 请在 @nestjs 目录下运行此脚本
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装依赖包...
npm install

REM 检查安装结果
if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功!
    echo.
    echo 可用的脚本命令:
    echo   npm run start:dev  - 启动开发服务器
    echo   npm run build      - 构建生产版本
    echo   npm run test       - 运行测试
    echo.
    echo 多语言功能测试:
    echo   node test-i18n.js  - 运行i18n自动化测试
    echo.
    echo 请确保配置 .env 文件后再启动应用
    echo 查看 I18N_GUIDE.md 了解多语言功能使用方法
) else (
    echo ❌ 依赖安装失败，请检查错误信息
    pause
    exit /b 1
)

pause
