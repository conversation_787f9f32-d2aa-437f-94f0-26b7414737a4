import { Router, Request, Response } from 'express';
import { ethers } from 'ethers';
import { ajv, tFromRequest, formatValidationErrors } from '../i18n';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { successResponse, errorResponse } from '../utils/responseUtil';
import dotenv from 'dotenv';

dotenv.config();

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义签名验证请求体验证模式
const signMessageSchema = {
  type: 'object',
  properties: {
    message: { type: 'string' }
  },
  required: ['message']
};

const validateSignMessage = ajv.compile(signMessageSchema);

// 定义验证签名请求体验证模式
const verifySignatureSchema = {
  type: 'object',
  properties: {
    message: { type: 'string' },
    signature: { type: 'string' },
    address: { type: 'string' }
  },
  required: ['message', 'signature']
};

const validateVerifySignature = ajv.compile(verifySignatureSchema);

/**
 * 使用服务器私钥签名消息
 * 路径: /api/web3-sign-test/sign
 * 方法: POST
 */
//@ts-ignore
router.post('/sign', async (req: Request, res: Response) => {
  try {

    // 验证请求体
    const valid = validateSignMessage(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.paramValidation'),
        formatValidationErrors(validateSignMessage.errors || [], req.language)
      ));
    }

    const { message } = req.body;
    
    
    // 从环境变量获取私钥（测试环境使用）
    const privateKey = process.env.TEST_ETH_PRIVATE_KEY;
    
    if (!privateKey) {
      return res.status(500).json(errorResponse(
        tFromRequest(req, 'errors.serverError'),
        'TEST_ETH_PRIVATE_KEY not set in environment variables'
      ));
    }

    // 使用私钥创建钱包实例
    const wallet = new ethers.Wallet(privateKey);
    
    // 签名消息
    const signature = await wallet.signMessage(message);
    
    // 返回签名结果和钱包地址
    return res.json(successResponse({
      signature,
      address: wallet.address,
      message
    }));
  } catch (error: any) {
    console.error('签名消息失败:', error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError'),
      error.message
    ));
  }
});

/**
 * 验证签名
 * 路径: /api/web3-sign-test/verify
 * 方法: POST
 */
//@ts-ignore
router.post('/verify', async (req: Request, res: Response) => {
  try {
    // 验证请求体
    const valid = validateVerifySignature(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.paramValidation'),
        formatValidationErrors(validateVerifySignature.errors || [], req.language)
      ));
    }

    const { message, signature, address } = req.body;
    
    // 使用ethers.js验证签名
    const recoveredAddress = ethers.verifyMessage(message, signature);
    
    // 如果提供了地址，则验证恢复的地址是否与提供的地址匹配
    const isValid = !address || recoveredAddress.toLowerCase() === address.toLowerCase();
    
    return res.json(successResponse({
      isValid,
      recoveredAddress,
      providedAddress: address || null,
      message
    }));
  } catch (error: any) {
    console.error('验证签名失败:', error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError'),
      error.message
    ));
  }
});

export default router;