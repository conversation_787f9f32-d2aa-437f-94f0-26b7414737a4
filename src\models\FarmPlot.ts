import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';
import { FarmPlotCalculator, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

interface FarmPlotAttributes {
  id: number;
  walletId: number;
  plotNumber: number; // 牧场区编号 1-20
  level: number; // 牧场区等级 1-20
  barnCount: number; // 牛舍数量
  milkProduction: number; // 每次产出获得的牛奶量
  productionSpeed: number; // 每次产出所需时间（秒）
  unlockCost: number; // 解锁费用
  upgradeCost: number; // 升级费用
  lastProductionTime: Date; // 上次产出时间
  isUnlocked: boolean; // 是否已解锁
  accumulatedMilk: number; // 累积的牛奶量
  createdAt?: Date;
  updatedAt?: Date;
}

interface FarmPlotCreationAttributes extends Optional<FarmPlotAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class FarmPlot extends Model<FarmPlotAttributes, FarmPlotCreationAttributes> implements FarmPlotAttributes {
  public id!: number;
  public walletId!: number;
  public plotNumber!: number;
  public level!: number;
  public barnCount!: number;
  public milkProduction!: number;
  public productionSpeed!: number;
  public unlockCost!: number;
  public upgradeCost!: number;
  public lastProductionTime!: Date;
  public isUnlocked!: boolean;
  public accumulatedMilk!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 计算基础产量
  public calculateBaseProduction(): number {
    // 使用新的配置数据查表方式获取产量
    // 产量值来自预设的配置数组，每个农场区块和等级都有独立的产量值
    return FarmPlotCalculator.calculateBaseProduction(this.level, this.plotNumber);
  }

  // 计算单个牛舍每秒产量
  public calculateSingleBarnProductionPerSecond(): number {
    // 单个牛舍产量（每秒） = 基礎 / 速度
    return this.calculateBaseProduction() / this.productionSpeed;
  }

  // 计算总产量（每秒）
  public calculateTotalProductionPerSecond(): number {
    // 总产量（每秒） = 单个产量 × 牛舍数量
    return this.calculateSingleBarnProductionPerSecond() * this.barnCount;
  }

  // 计算基础升级费用
  public calculateBaseUpgradeCost(): number {
    // 基础费用 = 200 (固定初始费用)
    return 200;
  }

  // 计算解锁费用
  public calculateUnlockCost(): number {
    // 使用BigNumber计算解锁费用，确保高精度
    const cost = FarmPlotCalculator.calculateUnlockCost(this.plotNumber);

    // 添加上限检查，防止超出INTEGER.UNSIGNED的最大值 (4,294,967,295)
    const MAX_UNLOCK_COST = 4294967295;
    return Math.min(cost, MAX_UNLOCK_COST);
  }

  // 升级牧场区
  public upgrade(): void {
    if (this.level >= 20) {
      throw new Error('牧场区已达到最高等级');
    }

    this.level += 1;

    // 使用新的配置数据进行计算
    this.barnCount = FarmPlotCalculator.calculateBarnCount(this.level);
    this.productionSpeed = FarmPlotCalculator.calculateProductionSpeed(this.level);
    this.milkProduction = FarmPlotCalculator.calculateBaseProduction(this.level, this.plotNumber);
    this.upgradeCost = FarmPlotCalculator.calculateUpgradeCostByLevel(this.plotNumber, this.level);
  }

  // 计算产量增长百分比
  public calculateProductionGrowthPercentage(): number {
    // 注意：现在使用配置数据，增长百分比不再固定
    // 这个方法保留用于兼容性，实际增长由配置数组决定
    return 50; // 产量增长1.5倍，即50%
  }

  // 计算速度增长百分比
  public calculateSpeedGrowthPercentage(): number {
    // 注意：现在使用配置数据，速度增长不再固定
    // 这个方法保留用于兼容性，实际速度由配置数组决定
    return 100 + (this.level - 1) * 5;
  }

  // 计算当前累积的牛奶量
  public calculateAccumulatedMilk(): number {
    const now = new Date();
    
    // 确保lastProductionTime是有效的Date对象
    if (!(this.lastProductionTime instanceof Date) || isNaN(this.lastProductionTime.getTime())) {
      this.lastProductionTime = new Date();
      return this.accumulatedMilk;
    }
    
    const timeDiff = (now.getTime() - this.lastProductionTime.getTime()) / 1000; // 转换为秒
    const cycles = Math.floor(timeDiff / this.productionSpeed); // 完成的生产周期数
    
    if (cycles <= 0) {
      return this.accumulatedMilk;
    }
    
    // 使用新的产量计算公式
    const baseProduction = this.calculateBaseProduction();
    const newMilk = cycles * baseProduction * this.barnCount;
    const totalMilk = this.accumulatedMilk + newMilk;
    
    // 更新上次产出时间
    this.lastProductionTime = new Date(this.lastProductionTime.getTime() + cycles * this.productionSpeed * 1000);
    
    return totalMilk;
  }

  // 收集牛奶
  public collectMilk(): number {
    // 确保lastProductionTime是有效的Date对象
    if (!(this.lastProductionTime instanceof Date) || isNaN(this.lastProductionTime.getTime())) {
      this.lastProductionTime = new Date();
    }
    
    const totalMilk = this.calculateAccumulatedMilk();
    this.accumulatedMilk = 0;
    return totalMilk;
  }
}

FarmPlot.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'user_wallets',
        key: 'id',
      },
    },
    plotNumber: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    level: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 1,
    },
    barnCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 1,
    },
    milkProduction: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      allowNull: false,
      defaultValue: 1,
    },
    productionSpeed: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 5, // 5秒/次
    },
    unlockCost: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 2000, // 初始解锁费用（编号2开始）
    },
    upgradeCost: {
      type: DataTypes.DECIMAL(20, 3),
      allowNull: false,
      defaultValue: 200, // 初始升级费用
    },
    lastProductionTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    isUnlocked: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    accumulatedMilk: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'farm_plots',
    timestamps: true,
  }
);



export { FarmPlot };