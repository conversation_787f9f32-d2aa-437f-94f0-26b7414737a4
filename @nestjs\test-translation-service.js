// 测试翻译服务
const axios = require('axios');

async function testTranslationService() {
  console.log('🔍 测试翻译服务...\n');
  
  try {
    // 测试一个已知存在的翻译键
    console.log('测试已知翻译键...');
    const response = await axios.get('http://localhost:3001/api/examples/i18n/translation-demo', {
      headers: {
        'Accept-Language': 'zh'
      }
    });
    
    console.log('翻译演示响应:');
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('翻译演示失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
  
  try {
    // 测试错误示例
    console.log('\n测试错误示例...');
    const response = await axios.get('http://localhost:3001/api/examples/i18n/error-examples', {
      headers: {
        'Accept-Language': 'zh'
      }
    });
    
    console.log('错误示例响应:');
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('错误示例失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testTranslationService();
