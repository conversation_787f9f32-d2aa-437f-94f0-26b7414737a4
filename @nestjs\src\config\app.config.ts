import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  // 应用基本配置
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.NESTJS_PORT, 10) || 3001,
  
  // JWT 配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret-key-here',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
  },
  
  // API 配置
  api: {
    prefix: 'api',
    version: '2.0',
    title: 'Wolf.Fun API',
    description: 'Wolf.Fun Game API Documentation',
  },
  
  // CORS 配置
  cors: {
    enabled: true,
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-language', 'x-wallet-address', 'x-wallet-signature'],
  },
}));
