import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface ChestBoostAttributes {
  id: number;
  sourceUserId: number; // 分享助力的用户
  sourceWalletId: number;
  //来源次代等级
  sourceLevel: number;
  targetUserId: number | null; // 被助力的用户
  targetWalletId: number | null;
  targetTelegramId?: string;
  boostType: string; // 'share' 分享助力 或 'referral' 推广助力
  boostMinutes: number; // 加速的分钟数
  chestLevel: number; // 宝箱等级
  gemAmount: number; // 奖励的宝石数量
  isProcessed: boolean; // 是否已处理
  expiresAt: Date; // 助力链接过期时间
  shareCodeId?: number | null; // 使用的分享码ID
  createdAt?: Date;
  updatedAt?: Date;
}

type ChestBoostCreationAttributes = Optional<ChestBoostAttributes, "id">;

export class ChestBoost
  extends Model<ChestBoostAttributes, ChestBoostCreationAttributes>
  implements ChestBoostAttributes
{
  public id!: number;
  public sourceUserId!: number;
  public sourceWalletId!: number;
  public sourceLevel!: number;
  public targetUserId!: number | null;
  public targetWalletId!: number | null;
  public targetTelegramId?: string;
  public boostType!: string;
  public boostMinutes!: number;
  public chestLevel!: number;
  public gemAmount!: number;
  public isProcessed!: boolean;
  public expiresAt!: Date;
  public shareCodeId?: number | null;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

ChestBoost.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    sourceUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    sourceWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    sourceLevel: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    targetUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    targetWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    targetTelegramId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    boostType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    boostMinutes: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    chestLevel: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    gemAmount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    isProcessed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    shareCodeId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: 'share_boost_links',
        key: 'id'
      },
    },
  },
  {
    tableName: "chest_boosts",
    sequelize,
    timestamps: true,
  }
);