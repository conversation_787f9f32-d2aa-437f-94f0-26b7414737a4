const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

async function testLoginFix() {
  try {
    console.log('🧪 测试修复后的Web3登录接口...\n');

    // 使用测试钱包地址（开发环境下会跳过签名验证）
    const testWalletAddress = '******************************************';
    
    console.log(`测试钱包地址: ${testWalletAddress}`);
    console.log('这是测试钱包地址，开发环境下会跳过签名验证\n');

    // 步骤1: 获取nonce
    console.log('1. 获取nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: testWalletAddress
    });

    if (!nonceResponse.data.ok) {
      throw new Error(`获取nonce失败: ${nonceResponse.data.message}`);
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取nonce成功: ${nonce.substring(0, 8)}...`);
    console.log(`消息: ${message.substring(0, 50)}...\n`);

    // 步骤2: 测试登录
    console.log('2. 测试登录...');
    const testSignature = '******************************************90abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
    
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: testWalletAddress,
      signature: testSignature,
      message: message,
      referralCode: undefined
    });

    if (loginResponse.data.ok) {
      console.log('✅ 登录成功！');
      console.log('响应数据:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
    } else {
      console.log('❌ 登录失败:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testLoginFix();
