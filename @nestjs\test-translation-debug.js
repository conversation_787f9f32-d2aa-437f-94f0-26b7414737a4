// 翻译服务调试测试
const axios = require('axios');

async function debugTranslation() {
  console.log('🔍 调试翻译服务...\n');
  
  const baseURL = 'http://localhost:3001/api/web3-auth';
  
  try {
    // 测试参数验证错误，获取详细的错误信息
    console.log('测试参数验证错误...');
    await axios.post(`${baseURL}/nonce`, {
      // 缺少 walletAddress
    }, {
      headers: {
        'Accept-Language': 'zh',
        'Content-Type': 'application/json'
      }
    });
    console.log('❌ 应该返回验证错误');
  } catch (error) {
    if (error.response) {
      console.log('完整错误响应:');
      console.log(JSON.stringify(error.response.data, null, 2));
      console.log('\n状态码:', error.response.status);
      console.log('响应头:', JSON.stringify(error.response.headers, null, 2));
    } else {
      console.log('网络错误:', error.message);
    }
  }
  
  // 测试一个正常的请求，看看是否有其他问题
  try {
    console.log('\n测试正常请求...');
    const response = await axios.post(`${baseURL}/nonce`, {
      walletAddress: '******************************************'
    }, {
      headers: {
        'Accept-Language': 'zh',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('正常请求响应:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('正常请求失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

debugTranslation();
