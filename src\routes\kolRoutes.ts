// src/routes/kolRoutes.ts
import { Router } from "express";
import { 
  getPersonalKolProgress, 
  getTeamKolProgress, 
  getDailyPromotionProgress,
  getPersonalKolStats,
  getTeamKolStats
} from "../services/kolProgressService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { RewardClaim } from "../models";
import { Op } from "sequelize";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义分页查询参数验证模式
const paginationQuerySchema = {
  type: "object",
  properties: {
    page: { type: "string", pattern: "^[0-9]+$" },
    limit: { type: "string", pattern: "^[0-9]+$" }
  }
};

const validatePaginationQuery = ajv.compile(paginationQuerySchema);

/**
 * GET /api/kol/personal-stats
 * 获取用户个人KOL推广统计数据
 */
router.get("/personal-stats", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    
    const stats = await getPersonalKolStats(userId);
    res.json(successResponse(stats, tFromRequest(req, "success.getPersonalKolStats")));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

/**
 * GET /api/kol/team-stats
 * 获取用户团队KOL推广统计数据
 */
router.get("/team-stats", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    
    const stats = await getTeamKolStats(userId);
    res.json(successResponse(stats, tFromRequest(req, "success.getTeamKolStats")));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

/**
 * GET /api/kol/personal-history
 * 获取用户全球个人KOL历史记录
 */
//@ts-ignore
router.get("/personal-history", walletAuthMiddleware, async (req, res) => {
  try {
    // 验证分页参数
    const valid = validatePaginationQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validatePaginationQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    
    if (!userId || !walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
    }

    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    
    if (isNaN(page) || page <= 0 || isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.invalidPagination")));
    }
    
    const offset = (page - 1) * limit;

    // 查询个人KOL相关的历史记录
    const { rows, count } = await RewardClaim.findAndCountAll({
      where: {
        userId,
        walletId,
        subPool: {
          [Op.like]: 'personal_kol_%'  // 匹配所有个人KOL相关的子池
        }
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    const pagination = {
      total: count,
      page,
      limit,
      totalPages
    };

    res.json(successResponse(
      { records: rows },
      tFromRequest(req, "success.getPersonalKolHistory"),
      pagination
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

/**
 * GET /api/kol/team-history
 * 获取用户全球团队KOL历史记录
 */
//@ts-ignore
router.get("/team-history", walletAuthMiddleware, async (req, res) => {
  try {
    // 验证分页参数
    const valid = validatePaginationQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validatePaginationQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    
    if (!userId || !walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
    }

    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    
    if (isNaN(page) || page <= 0 || isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.invalidPagination")));
    }
    
    const offset = (page - 1) * limit;

    // 查询团队KOL相关的历史记录
    const { rows, count } = await RewardClaim.findAndCountAll({
      where: {
        userId,
        walletId,
        subPool: {
          [Op.like]: 'team_kol_%'  // 匹配所有团队KOL相关的子池
        }
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    const pagination = {
      total: count,
      page,
      limit,
      totalPages
    };

    res.json(successResponse(
      { records: rows },
      tFromRequest(req, "success.getTeamKolHistory"),
      pagination
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;