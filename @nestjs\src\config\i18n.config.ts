import { registerAs } from '@nestjs/config';
import * as path from 'path';

export default registerAs('i18n', () => ({
  // 默认语言
  fallbackLanguage: 'en',

  // 支持的语言列表
  supportedLanguages: ['en', 'zh', 'zh-tw', 'ja'],
  
  // 翻译文件路径
  loaderOptions: {
    path: path.join(__dirname, '../i18n/'),
    watch: process.env.NODE_ENV === 'development',
  },
  
  // 语言解析器配置 - 简化为只使用 Accept-Language 请求头
  // 移除了 x-language、查询参数、Cookie 等其他检测方式
  resolvers: [
    // 只从 Accept-Language 请求头解析语言，符合HTTP标准
    {
      use: 'header',
      options: ['accept-language'],
    },
  ],
  
  // 格式化选项
  formatter: 'icu',
  
  // 日志选项
  logging: process.env.NODE_ENV === 'development',
  
  // 抛出缺失键的错误
  throwOnMissingKey: process.env.NODE_ENV === 'development',
  
  // 类型安全选项
  typesOutputPath: path.join(__dirname, '../generated/i18n.generated.ts'),
}));
