// src/models/BullKingRecord.ts
import { Model, DataTypes } from "sequelize";
import { sequelize } from "../config/db";

export interface BullKingRecordAttributes {
  id?: number;
  userId: number;
  walletId: number;
  transformCount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class BullKingRecord extends Model<BullKingRecordAttributes> implements BullKingRecordAttributes {
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public transformCount!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

BullKingRecord.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    transformCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "bull_king_records",
    sequelize,
    timestamps: true,
  }
);

export default BullKingRecord;