import { HttpException, HttpStatus } from '@nestjs/common';

export class I18nException extends HttpException {
  constructor(
    messageKey: string,
    variables?: Record<string, any>,
    status: HttpStatus = HttpStatus.BAD_REQUEST,
  ) {
    super(
      {
        messageKey,
        variables,
        timestamp: new Date().toISOString(),
      },
      status,
    );
  }
}

export class I18nBadRequestException extends I18nException {
  constructor(messageKey: string, variables?: Record<string, any>) {
    super(messageKey, variables, HttpStatus.BAD_REQUEST);
  }
}

export class I18nUnauthorizedException extends I18nException {
  constructor(messageKey: string = 'errors.unauthorized', variables?: Record<string, any>) {
    super(messageKey, variables, HttpStatus.UNAUTHORIZED);
  }
}

export class I18nForbiddenException extends I18nException {
  constructor(messageKey: string = 'errors.forbidden', variables?: Record<string, any>) {
    super(messageKey, variables, HttpStatus.FORBIDDEN);
  }
}

export class I18nNotFoundException extends I18nException {
  constructor(messageKey: string = 'errors.not_found', variables?: Record<string, any>) {
    super(messageKey, variables, HttpStatus.NOT_FOUND);
  }
}
