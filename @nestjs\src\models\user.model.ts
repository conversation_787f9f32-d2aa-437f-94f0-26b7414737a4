import { Table, Column, Model, DataType, HasMany, BelongsTo } from 'sequelize-typescript';
import { UserWallet } from './user-wallet.model';

@Table({
  tableName: 'users',
  timestamps: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
})
export class User extends Model<User> {
  @Column({
    type: DataType.INTEGER.UNSIGNED,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  // Telegram 相关字段（已废弃，但保留兼容性）
  @Column({
    type: DataType.STRING,
    allowNull: true,
    unique: false,
  })
  telegramId?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: null,
  })
  hasFollowedChannel?: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  authDate?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  hash?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: null,
  })
  telegram_premium?: boolean;

  // 基本用户信息
  @Column(DataType.STRING)
  username?: string;

  @Column(DataType.STRING)
  firstName?: string;

  @Column(DataType.STRING)
  lastName?: string;

  @Column(DataType.STRING)
  photoUrl?: string;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    allowNull: false,
    defaultValue: 0,
  })
  referralCount?: number;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    allowNull: true,
  })
  referrerId?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  refWalletAddress?: string;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    allowNull: true,
  })
  firstWalletId?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      isEmail: true,
    },
  })
  email?: string;

  // 关联关系
  @HasMany(() => UserWallet, { foreignKey: 'userId', as: 'wallets' })
  wallets: UserWallet[];

  @BelongsTo(() => User, { foreignKey: 'referrerId', as: 'referrer' })
  referrer: User;

  @HasMany(() => User, { foreignKey: 'referrerId', as: 'referrals' })
  referrals: User[];
}
