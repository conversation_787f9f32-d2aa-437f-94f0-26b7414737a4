// src/models/MissedRebate.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface MissedRebateAttributes {
  id: number;
  userId: number;
  walletId: number;
  amount: number;
  rebateCount: number;
  reason: string;
  date: string;
  createdAt: Date;
  updatedAt: Date;
}

type MissedRebateCreationAttributes = Optional<MissedRebateAttributes, "id">;

export class MissedRebate
  extends Model<MissedRebateAttributes, MissedRebateCreationAttributes>
  implements MissedRebateAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public amount!: number;
  public rebateCount!: number;
  public reason!: string;
  public date!: string;
  public createdAt!: Date;
  public updatedAt!: Date;
}

MissedRebate.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    rebateCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: "missed_rebates",
    sequelize,
    timestamps: true,
  }
);