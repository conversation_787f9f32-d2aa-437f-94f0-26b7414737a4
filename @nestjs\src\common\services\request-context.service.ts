import { Injectable, Scope } from '@nestjs/common';

export interface RequestUser {
  userId: number;
  walletId?: number;
  walletAddress?: string;
  username?: string;
}

export interface RequestContext {
  language: string;
  user?: RequestUser;
}

@Injectable({ scope: Scope.REQUEST })
export class RequestContextService {
  private context: RequestContext = {
    language: 'en',
  };

  setLanguage(language: string): void {
    this.context.language = language;
  }

  getLanguage(): string {
    return this.context.language;
  }

  setUser(user: RequestUser): void {
    this.context.user = user;
  }

  getUser(): RequestUser | undefined {
    return this.context.user;
  }

  getContext(): RequestContext {
    return this.context;
  }

  setContext(context: Partial<RequestContext>): void {
    this.context = { ...this.context, ...context };
  }
}
