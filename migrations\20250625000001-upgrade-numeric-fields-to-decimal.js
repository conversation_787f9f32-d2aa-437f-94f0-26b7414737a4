'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始升级数值字段为DECIMAL类型以支持大数值...');
    
    try {
      // 1. 升级 user_wallets 表中的数值字段
      console.log('升级 user_wallets 表...');
      
      // 检查表结构
      const userWalletColumns = await queryInterface.describeTable('user_wallets');
      
      // 升级 gem 字段 - 使用最大精度支持超大数值
      if (userWalletColumns.gem) {
        await queryInterface.changeColumn('user_wallets', 'gem', {
          type: Sequelize.DECIMAL(65, 3), // 65位总长度，3位小数 - 支持最大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.gem 字段已升级为 DECIMAL(65,3) - 支持超大数值');
      }

      // 升级 milk 字段 - 使用最大精度支持超大数值
      if (userWalletColumns.milk) {
        await queryInterface.changeColumn('user_wallets', 'milk', {
          type: Sequelize.DECIMAL(65, 3), // 65位总长度，3位小数 - 支持最大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.milk 字段已升级为 DECIMAL(65,3) - 支持超大数值');
      }
      
      // 升级其他可能很大的数值字段
      if (userWalletColumns.ton) {
        await queryInterface.changeColumn('user_wallets', 'ton', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.ton 字段已升级为 DECIMAL(65,3)');
      }

      if (userWalletColumns.usd) {
        await queryInterface.changeColumn('user_wallets', 'usd', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.usd 字段已升级为 DECIMAL(65,3)');
      }

      if (userWalletColumns.moof) {
        await queryInterface.changeColumn('user_wallets', 'moof', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.moof 字段已升级为 DECIMAL(65,3)');
      }

      if (userWalletColumns.unlockMoof) {
        await queryInterface.changeColumn('user_wallets', 'unlockMoof', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.unlockMoof 字段已升级为 DECIMAL(65,3)');
      }

      if (userWalletColumns.bullReward) {
        await queryInterface.changeColumn('user_wallets', 'bullReward', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ user_wallets.bullReward 字段已升级为 DECIMAL(65,3)');
      }
      
      // 2. 升级 delivery_lines 表中的数值字段
      console.log('升级 delivery_lines 表...');
      
      const deliveryLineColumns = await queryInterface.describeTable('delivery_lines');
      
      if (deliveryLineColumns.pendingMilk) {
        await queryInterface.changeColumn('delivery_lines', 'pendingMilk', {
          type: Sequelize.DECIMAL(65, 3), // 支持超大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.pendingMilk 字段已升级为 DECIMAL(65,3)');
      }

      if (deliveryLineColumns.pendingBlocks) {
        await queryInterface.changeColumn('delivery_lines', 'pendingBlocks', {
          type: Sequelize.DECIMAL(65, 3), // 支持超大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.pendingBlocks 字段已升级为 DECIMAL(65,3)');
      }
      
      if (deliveryLineColumns.deliverySpeed) {
        await queryInterface.changeColumn('delivery_lines', 'deliverySpeed', {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.deliverySpeed 字段已升级为 DECIMAL(15,3)');
      }
      
      if (deliveryLineColumns.blockUnit) {
        await queryInterface.changeColumn('delivery_lines', 'blockUnit', {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.blockUnit 字段已升级为 DECIMAL(15,3)');
      }
      
      if (deliveryLineColumns.blockPrice) {
        await queryInterface.changeColumn('delivery_lines', 'blockPrice', {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.blockPrice 字段已升级为 DECIMAL(15,3)');
      }
      
      if (deliveryLineColumns.upgradeCost) {
        await queryInterface.changeColumn('delivery_lines', 'upgradeCost', {
          type: Sequelize.DECIMAL(20, 3),
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ delivery_lines.upgradeCost 字段已升级为 DECIMAL(20,3)');
      }

      // 3. 升级 farm_plots 表中的数值字段
      console.log('升级 farm_plots 表...');

      const farmPlotColumns = await queryInterface.describeTable('farm_plots');

      if (farmPlotColumns.milkProduction) {
        await queryInterface.changeColumn('farm_plots', 'milkProduction', {
          type: Sequelize.DECIMAL(65, 3), // 支持超大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ farm_plots.milkProduction 字段已升级为 DECIMAL(65,3)');
      }

      if (farmPlotColumns.productionSpeed) {
        await queryInterface.changeColumn('farm_plots', 'productionSpeed', {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          defaultValue: 5,
        });
        console.log('✅ farm_plots.productionSpeed 字段已升级为 DECIMAL(15,3)');
      }

      if (farmPlotColumns.upgradeCost) {
        await queryInterface.changeColumn('farm_plots', 'upgradeCost', {
          type: Sequelize.DECIMAL(20, 3),
          allowNull: false,
          defaultValue: 200,
        });
        console.log('✅ farm_plots.upgradeCost 字段已升级为 DECIMAL(20,3)');
      }

      if (farmPlotColumns.accumulatedMilk) {
        await queryInterface.changeColumn('farm_plots', 'accumulatedMilk', {
          type: Sequelize.DECIMAL(65, 3), // 支持超大数值
          allowNull: false,
          defaultValue: 0,
        });
        console.log('✅ farm_plots.accumulatedMilk 字段已升级为 DECIMAL(65,3)');
      }

      console.log('🎉 所有数值字段升级完成！');

    } catch (error) {
      console.error('升级数值字段时发生错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('回滚数值字段升级...');
    
    try {
      // 回滚 user_wallets 表
      await queryInterface.changeColumn('user_wallets', 'gem', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'milk', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'ton', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'usd', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'moof', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'unlockMoof', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('user_wallets', 'bullReward', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      // 回滚 delivery_lines 表
      await queryInterface.changeColumn('delivery_lines', 'pendingMilk', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('delivery_lines', 'pendingBlocks', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('delivery_lines', 'deliverySpeed', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('delivery_lines', 'blockUnit', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('delivery_lines', 'blockPrice', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });
      
      await queryInterface.changeColumn('delivery_lines', 'upgradeCost', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });

      // 回滚 farm_plots 表
      await queryInterface.changeColumn('farm_plots', 'milkProduction', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 1,
      });

      await queryInterface.changeColumn('farm_plots', 'productionSpeed', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 5,
      });

      await queryInterface.changeColumn('farm_plots', 'upgradeCost', {
        type: Sequelize.INTEGER.UNSIGNED, // 原来是 INTEGER.UNSIGNED
        allowNull: false,
        defaultValue: 200,
      });

      await queryInterface.changeColumn('farm_plots', 'accumulatedMilk', {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      });

      console.log('数值字段回滚完成');
    } catch (error) {
      console.error('回滚数值字段时发生错误:', error);
      throw error;
    }
  },
};
