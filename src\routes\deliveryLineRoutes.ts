import express from 'express';
import deliveryLineController from '../controllers/deliveryLineController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';

const router = express.Router();

// 所有路由都需要钱包认证
router.use(walletAuthMiddleware);

// 获取用户的出货线
router.get('/delivery-line', deliveryLineController.getUserDeliveryLine);

// 升级出货线
router.post('/delivery-line/upgrade', deliveryLineController.upgradeDeliveryLine);

// 添加牛奶到出货线
router.post('/delivery-line/add-milk', deliveryLineController.addMilkToDeliveryLine);

// 计算离线收益
router.post('/delivery-line/offline-earnings', deliveryLineController.calculateOfflineEarnings);

// 出售牛奶方块换取宝石
router.post('/delivery-line/sell-blocks', deliveryLineController.sellMilkBlocks);

export default router;