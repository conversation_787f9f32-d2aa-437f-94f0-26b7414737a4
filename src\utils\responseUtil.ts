// 成功响应
export function successResponse(data: any = null, message: any = undefined, pagination: any = undefined) {
  return {
    ok: true,
    data,
    message,
    pagination
  };
}

// 错误响应
export function errorResponse(message: string, error: any = undefined) {
  return {
    ok: false,
    message,
    error
  };
}

// 响应工具类
export const responseUtil = {
  success: (res: any, data: any = null, message: any = undefined, pagination: any = undefined) => {
    res.json(successResponse(data, message, pagination));
  },
  error: (res: any, message: string, statusCode: number = 400, error: any = undefined) => {
    res.status(statusCode).json(errorResponse(message, error));
  }
};