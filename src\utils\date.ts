import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);
/**
 * 将传入的时间转换为中国时区时间
 * @param {string | Date | number} time 传入的时间，可以是时间字符串、Date 对象或时间戳
 * @param {string} format 期望返回的格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的中国时区时间字符串
 */
export function convertToChinaTime(time: any, format = "YYYY-MM-DD HH:mm:ss") {
  // 先解析传入的时间，再转换到中国时区
  const chinaTime = dayjs(time).tz("Asia/Shanghai");
  return chinaTime.format(format);
}
