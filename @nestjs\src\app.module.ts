import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { BullModule } from '@nestjs/bull';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { RedisModule } from './redis/redis.module';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { I18nModule } from './i18n/i18n.module';
import { ExamplesModule } from './examples/examples.module';
import { Web3AuthModule } from './web3-auth/web3-auth.module';
import { Web3SignTestModule } from './web3-sign-test/web3-sign-test.module';
import { UserModule } from './user/user.module';
import { appConfig, databaseConfig, redisConfig, i18nConfig, validationSchema } from './config';

@Module({
  imports: [
    // 配置模块 - 全局配置
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [appConfig, databaseConfig, redisConfig, i18nConfig],
      validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
    
    // 数据库模块
    DatabaseModule,
    
    // Redis 模块
    RedisModule,
    
    // BullMQ 队列模块
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const redisConfig = configService.get('redis');
        return {
          redis: {
            host: redisConfig.host,
            port: redisConfig.port,
            password: redisConfig.password,
            db: redisConfig.db,
            ...redisConfig.options,
          },
        };
      },
      inject: [ConfigService],
    }),
    
    // 国际化模块
    I18nModule,

    // 业务模块
    AuthModule,
    CommonModule,
    UserModule,
    Web3AuthModule,
    Web3SignTestModule,

    // 示例模块
    ExamplesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
