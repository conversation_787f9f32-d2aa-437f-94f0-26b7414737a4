# NestJS 快速启动指南

## 第一阶段已完成 ✅

基础的NestJS框架已经搭建完成，包含以下功能：

- ✅ 基础项目结构
- ✅ 配置管理系统
- ✅ 数据库集成（Sequelize）
- ✅ Redis 和队列系统
- ✅ 认证系统（JWT + 钱包认证）
- ✅ 中间件和守卫
- ✅ API 文档（Swagger）
- ✅ 多语言国际化（nestjs-i18n）

## 快速启动步骤

### 1. 安装依赖

```bash
cd @nestjs

# Windows
install-dependencies.bat

# Linux/macOS
chmod +x install-dependencies.sh
./install-dependencies.sh
```

### 2. 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和Redis连接信息：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=wolf_fun

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
```

### 3. 启动应用

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 4. 访问API文档

启动后访问：http://localhost:3001/api/docs

## 测试API

### 健康检查
```bash
curl http://localhost:3001/api/health
```

### 应用信息
```bash
curl http://localhost:3001/api
```

### 多语言测试
```bash
# 中文简体
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/health

# 中文繁体
curl -H "Accept-Language: zh-TW,zh-tw;q=0.9,zh;q=0.8,en;q=0.7" http://localhost:3001/api/health

# 日语
curl -H "Accept-Language: ja-JP,ja;q=0.9,en;q=0.8" http://localhost:3001/api/health

# I18n 功能测试
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/i18n-test

# 验证错误测试
curl -X POST -H "Content-Type: application/json" -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" \
  -d '{"name":"","email":"invalid"}' \
  http://localhost:3001/api/test-validation

# I18n 异常测试
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/test-error

# 示例接口测试
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://localhost:3001/api/examples/i18n/translation-demo

# 运行自动化测试
node test-i18n.js
```

## 下一步

第一阶段已完成！现在可以开始第二阶段：

**请指定您希望首先重构的API接口**，例如：
- 用户相关接口
- 钱包相关接口  
- 游戏相关接口
- 任务相关接口
- 等等...

我将根据您的指示，逐个重构现有的Express API接口到NestJS架构中。
