'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 检查字段是否已存在
      const tableInfo = await queryInterface.describeTable('user_wallets');
      
      // 添加free_ticket字段（如果不存在）
      if (!tableInfo.free_ticket) {
        await queryInterface.addColumn('user_wallets', 'free_ticket', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true
        });
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 移除添加的字段
      await queryInterface.removeColumn('user_wallets', 'free_ticket');
    }
  }
};