// src/models/User.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface UserAttributes {
  id: number;
  // Telegram 相关字段（已废弃，但保留兼容性）
  telegramId?: string;
  hasFollowedChannel?: boolean;
  authDate?: number;
  hash?: string;
  telegram_premium?: boolean;
  // 基本用户信息
  username?: string;
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  referralCount?: number;
  referrerId?: number;
  refWalletAddress?: string;
  firstWalletId?: number;
  email?: string; // 新增邮箱字段

}

type UserCreationAttributes = Optional<UserAttributes, "id">;

export class User
  extends Model<UserAttributes, UserCreationAttributes>
  implements UserAttributes
{
  public id!: number;
  // Telegram 相关字段（已废弃，但保留兼容性）
  public telegramId?: string;
  public hasFollowedChannel?: boolean;
  public authDate?: number;
  public hash?: string;
  public telegram_premium?: boolean;
  // 基本用户信息
  public username?: string;
  public firstName?: string;
  public lastName?: string;
  public photoUrl?: string;
  public referralCount?: number;
  public referrerId?: number;
  public firstWalletId?: number;
  public refWalletAddress?: string;
  public email?: string; // 新增邮箱字段

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    // Telegram 相关字段（已废弃，但保留兼容性）
    telegramId: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: false,
    },
    hasFollowedChannel: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: null,
    },
    authDate: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    hash: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    telegram_premium: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: null,
    },

    referralCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    referrerId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    firstWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },

    username: DataTypes.STRING,

    refWalletAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },

    firstName: DataTypes.STRING,
    lastName: DataTypes.STRING,
    photoUrl: DataTypes.STRING,
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true, // 添加邮箱格式验证
      },
    },
    
  },
  {
    tableName: "users",
    sequelize,
    timestamps: true,
  }
);
