import { Injectable } from '@nestjs/common';
import { User } from '../models/user.model';
import { UserWallet } from '../models/user-wallet.model';
import { CustomI18nService, SupportedLanguage } from '../i18n/i18n.service';
import { UserInfoResponseDto } from './dto/user-info.dto';

@Injectable()
export class UserService {
  constructor(
    private readonly i18nService: CustomI18nService,
  ) {}

  /**
   * 获取用户信息
   * 复制原userInfo函数的完整业务逻辑
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @param language 语言代码（可选）
   * @returns 用户信息数组
   */
  async getUserInfo(userId: number, walletId: number, language?: string): Promise<UserInfoResponseDto[]> {
    // 查找用户
    const user = await User.findByPk(userId);

    if (!user) {
      const lang = language as SupportedLanguage || 'en';
      throw new Error(this.i18nService.translate('errors.userNotFound', {}, lang));
    }

    const users: UserInfoResponseDto[] = [];

    // 查找钱包
    const wallet = await UserWallet.findByPk(walletId);

    // 查找推荐人信息
    let refUser: User | null = null;
    if (user.referrerId) {
      refUser = await User.findByPk(user.referrerId);
    }

    // 组装用户信息，保持与原接口完全一致的数据结构
    users.push({
      referralCount: user.referralCount,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      photoUrl: user.photoUrl,
      walletAddress: wallet?.walletAddress,
      referral: {
        username: refUser?.username,
        firstName: refUser?.firstName,
        lastName: refUser?.lastName,
        photoUrl: refUser?.photoUrl,
        walletAddress: user.refWalletAddress,
      },
      gem: wallet?.gem,
      usd: wallet?.usd,
      ticket: wallet?.ticket,
      free_ticket: wallet?.free_ticket,
      moof: wallet?.moof,
      unlockMoof: wallet?.unlockMoof,
      fragment_green: wallet?.fragment_green,
      fragment_blue: wallet?.fragment_blue,
      fragment_purple: wallet?.fragment_purple,
      fragment_gold: wallet?.fragment_gold,
      code: wallet?.code,
      createdAt: user.createdAt,
      email: user.email
    });

    return users;
  }
}
