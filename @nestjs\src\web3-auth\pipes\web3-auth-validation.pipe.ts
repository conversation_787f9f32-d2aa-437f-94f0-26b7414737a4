import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { I18nContext } from 'nestjs-i18n';
import { TranslationService } from '../../common/services/translation.service';
import { SupportedLanguage } from '../../i18n/i18n.service';

@Injectable()
export class Web3AuthValidationPipe implements PipeTransform<any> {
  constructor(private readonly translationService: TranslationService) {}

  async transform(value: any, { metatype }: ArgumentMetadata) {
    console.log('🔍 Web3AuthValidationPipe called with value:', value);

    if (!metatype || !this.toValidate(metatype)) {
      console.log('🔍 Skipping validation for metatype:', metatype?.name);
      return value;
    }

    const object = plainToClass(metatype, value);
    const errors = await validate(object);

    console.log('🔍 Validation errors:', errors);

    if (errors.length > 0) {
      // 格式化验证错误，与原版本AJV格式保持一致
      const formattedErrors = errors.map(error => ({
        field: error.property,
        message: Object.values(error.constraints || {})[0] || 'Validation failed',
      }));

      // 获取当前语言上下文
      const i18nContext = I18nContext.current();
      const currentLang = (i18nContext?.lang || 'en') as SupportedLanguage;

      // 获取翻译后的错误消息
      const errorMessage = this.translationService.t(
        'errors.paramValidation',
        {},
        currentLang
      );

      // 抛出与原版本格式完全一致的错误
      throw new BadRequestException({
        ok: false,
        message: errorMessage,
        error: formattedErrors,
      });
    }

    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
