'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始更新农场区公式...');
    
    // 获取所有农场区数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${farmPlots.length} 个农场区记录`);
    
    // 批量更新农场区数据
    for (const plot of farmPlots) {
      // 重新计算产量：1 × (1.5) ^ (等级 - 1)
      const newMilkProduction = 1 * Math.pow(1.5, plot.level - 1);
      
      // 重新计算生产速度：从等级1的5秒开始，每级提升5%
      // 等级1=5秒，等级2=5/1.05=4.76秒，等级3=4.76/1.05=4.54秒...
      const newProductionSpeed = 5 / Math.pow(1.05, plot.level - 1);
      
      // 重新计算升级费用：200 × (1.5) ^ (等级 - 1)
      const newUpgradeCost = 200 * Math.pow(1.5, plot.level - 1);
      
      // 更新数据库
      await queryInterface.sequelize.query(
        `UPDATE farm_plots SET 
           milkProduction = :milkProduction,
           productionSpeed = :productionSpeed,
           upgradeCost = :upgradeCost
         WHERE id = :id`,
        {
          replacements: {
            milkProduction: newMilkProduction,
            productionSpeed: newProductionSpeed,
            upgradeCost: newUpgradeCost,
            id: plot.id
          },
          type: Sequelize.QueryTypes.UPDATE
        }
      );
    }
    
    console.log('农场区公式更新完成');
    
    // 显示更新后的示例数据
    console.log('\n更新后的数据示例:');
    console.log('等级1: 产量=1.0, 速度=5.00秒, 费用=200');
    console.log('等级2: 产量=1.5, 速度=4.76秒, 费用=300');
    console.log('等级3: 产量=2.25, 速度=4.54秒, 费用=450');
    console.log('等级4: 产量=3.38, 速度=4.32秒, 费用=675');
    console.log('等级5: 产量=5.06, 速度=4.11秒, 费用=1013');
  },

  down: async (queryInterface, Sequelize) => {
    console.log('开始回滚农场区公式...');
    
    // 获取所有农场区数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${farmPlots.length} 个农场区记录`);
    
    // 回滚到旧的公式
    for (const plot of farmPlots) {
      // 旧的产量公式：1 × (2.0) ^ (編號 - 1) × (1.5) ^ (等级 - 1)
      const oldMilkProduction = 1 * Math.pow(2.0, plot.plotNumber - 1) * Math.pow(1.5, plot.level - 1);
      
      // 旧的生产速度：从等级1的5秒开始，每级增加5%
      const oldProductionSpeed = 5 * Math.pow(1.05, plot.level - 1);
      
      // 旧的升级费用计算
      const baseUpgradeCost = 200;
      const oldUpgradeCost = baseUpgradeCost * Math.pow(1.5, plot.level);
      
      // 更新数据库
      await queryInterface.sequelize.query(
        `UPDATE farm_plots SET 
           milkProduction = :milkProduction,
           productionSpeed = :productionSpeed,
           upgradeCost = :upgradeCost
         WHERE id = :id`,
        {
          replacements: {
            milkProduction: oldMilkProduction,
            productionSpeed: oldProductionSpeed,
            upgradeCost: oldUpgradeCost,
            id: plot.id
          },
          type: Sequelize.QueryTypes.UPDATE
        }
      );
    }
    
    console.log('农场区公式回滚完成');
  }
};
