// src/models/AccountSubscriptionState.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface AccountSubscriptionStateAttributes {
  id: number;
  accountAddress: string;
  lastIndexedLt: string;
  createdAt?: Date;
  updatedAt?: Date;
}

type AccountSubscriptionStateCreationAttributes = Optional<AccountSubscriptionStateAttributes, "id">;

export class AccountSubscriptionState
  extends Model<AccountSubscriptionStateAttributes, AccountSubscriptionStateCreationAttributes>
  implements AccountSubscriptionStateAttributes
{
  public id!: number;
  public accountAddress!: string;
  public lastIndexedLt!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

AccountSubscriptionState.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    accountAddress: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    lastIndexedLt: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "account_subscription_states",
    timestamps: true,
  }
);