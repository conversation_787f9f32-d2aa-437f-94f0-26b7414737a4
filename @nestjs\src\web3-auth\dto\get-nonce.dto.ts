import { IsString, <PERSON>NotEmpty, IsOptional } from 'class-validator';

export class GetNonceDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;
}

export class NonceResponseDto {
  nonce: string;
  message: string;
}

export class Web3LoginDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;

  @IsString()
  @IsNotEmpty()
  signature: string;

  @IsString()
  @IsNotEmpty()
  message: string;

  @IsString()
  @IsOptional()
  referralCode?: string;
}

export class Web3LoginResponseDto {
  token: string;
  user: {
    id: number;
    username: string;
    walletAddress: string;
  };
}
