#!/usr/bin/env node

/**
 * 健康检查测试
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testHealth() {
  console.log('🔍 测试健康检查...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过');
    console.log(JSON.stringify(healthResponse.data, null, 2));
    console.log('');

    // 测试 Web3Auth 健康检查
    console.log('2. 测试 Web3Auth 健康检查...');
    const web3HealthResponse = await axios.get(`${BASE_URL}/web3-auth/health`);
    console.log('✅ Web3Auth 健康检查通过');
    console.log(JSON.stringify(web3HealthResponse.data, null, 2));
    console.log('');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testHealth().catch(console.error);
