'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 要插入的初始任务数据
    const tasks = [
      {
        name: '每日奖励宝箱',
        type: 'DAILY_SIGNIN',
        repeatInterval: 'day',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '加入我们的Telegram频道宝箱',
        type: 'JOIN_TELEGRAM',
        repeatInterval: 'once',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '关注我们的X宝箱',
        type: 'FOLLOW_X',
        repeatInterval: 'once',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // 逐条检查是否已存在(根据 name 字段)，若不存在才插入
    for (const t of tasks) {
      // rawSelect: 查询一条记录的某个字段(这里查 id)，不存在则返回 null
      const existing = await queryInterface.rawSelect('tasks', {
        where: { name: t.name },
      }, ['id']);

      // 如果数据库中没查到 => 执行插入
      if (!existing) {
        await queryInterface.bulkInsert('tasks', [t], {});
      }
    }
  },

  // 如果想做回退逻辑，可以在 down() 里删除这些任务
  async down(queryInterface, Sequelize) {
    // 这里演示删除我们上面插入过的
    await queryInterface.bulkDelete('tasks', {
      name: [
        '每日签到',
        '加入Telegram频道'
      ]
    }, {});
  }
};