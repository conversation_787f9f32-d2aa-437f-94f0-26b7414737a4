import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('app.jwt.secret') ||
                   configService.get('JWT_SECRET') ||
                   'your-secret-key',
    });
  }

  async validate(payload: any) {
    return {
      userId: payload.userId || payload.sub,
      username: payload.username,
      walletId: payload.walletId,
      walletAddress: payload.walletAddress,
    };
  }
}

// 钱包专用JWT策略
@Injectable()
export class WalletJwtStrategy extends PassportStrategy(Strategy, 'wallet-jwt') {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET_Wallet') ||
                   configService.get('app.jwt.secret') ||
                   'your-secret-key',
    });
  }

  async validate(payload: any) {
    return {
      userId: payload.userId,
      walletId: payload.walletId,
      walletAddress: payload.walletAddress,
    };
  }
}
