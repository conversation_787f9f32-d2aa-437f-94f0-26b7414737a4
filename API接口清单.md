# MooFun 项目 API 接口清单

### 按字母顺序排列的所有端点

| 端点 | 方法 | 模块 | 用途 |
|------|------|------|------|
| `/api/delivery/delivery-line` | GET | 游戏玩法 | 获取配送线信息 |
| `/api/delivery/delivery-line/upgrade` | POST | 游戏玩法 | 升级配送线 |
| `/api/farm/farm-plots` | GET | 游戏玩法 | 获取农场地块 |
| `/api/farm/farm-plots/unlock` | POST | 游戏玩法 | 解锁农场地块 |
| `/api/farm/farm-plots/upgrade` | POST | 游戏玩法 | 升级农场地块 |
| `/api/fragment/craft-ticket` | POST | 库存管理 | 制作门票 |
| `/api/free-ticket/remaining-limit` | GET | 门票转移 | 获取转移限制 |
| `/api/free-ticket/transfer` | POST | 门票转移 | 转移门票 |
| `/api/gem-leaderboard` | GET | 排行榜 | 宝石排行榜 |
| `/api/iap/boosters` | GET | 应用内购买 | 获取增强道具 |
| `/api/iap/boosters/active` | GET | 应用内购买 | 获取激活道具 |
| `/api/iap/boosters/use` | POST | 应用内购买 | 使用增强道具 |
| `/api/iap/payment/create` | POST | 应用内购买 | 创建支付 |
| `/api/iap/purchase/history` | GET | 应用内购买 | 购买历史 |
| `/api/iap/store/products` | GET | 应用内购买 | 商店产品 |
| `/api/iap/vip/status` | GET | 应用内购买 | VIP状态 |
| `/api/invite/claim-daily` | POST | 推荐系统 | 领取每日奖励 |
| `/api/jackpot-chest/accelerate` | POST | 倒计时宝箱 | 加速倒计时 |
| `/api/jackpot-chest/collect` | POST | 倒计时宝箱 | 收集宝箱 |
| `/api/jackpot-chest/countdown` | GET | 倒计时宝箱 | 获取宝箱状态 |
| `/locales/{locale}.json` | GET | 国际化 | 加载语言文件 |
| `/api/referral/bind` | POST | 推荐系统 | 绑定推荐码 |
| `/api/referral/downline` | GET | 推荐系统 | 获取下线列表 |
| `/api/referral/list` | GET | 推荐系统 | 获取推荐列表 |
| `/api/referral/open` | POST | 推荐系统 | 打开推荐宝箱 |
| `/api/referral/referral-chests/count` | GET | 推荐系统 | 推荐宝箱数量 |
| `/api/referral/status` | GET | 推荐系统 | 推荐状态 |
| `/api/tasks` | GET | 任务系统 | 获取任务列表 |
| `/api/tasks/complete` | POST | 任务系统 | 完成任务 |
| `/api/telegram-share/boost` | POST | 倒计时宝箱 | 处理提升链接 |
| `/api/user/me` | GET | 用户信息 | 获取用户信息 |
| `/api/wallet/batch-update-resources` | POST | 游戏玩法 | 批量更新资源 |
| `/api/wallet/claim-offline-reward` | POST | 游戏玩法 | 领取离线奖励 |
| `/api/wallet/increase-gem` | POST | 游戏玩法 | 增加宝石 |
| `/api/wallet/increase-milk` | POST | 游戏玩法 | 增加牛奶 |
| `/api/wallet/offline-reward` | GET | 游戏玩法 | 获取离线奖励 |
| `/api/web3-auth/login` | POST | 用户认证 | 验证签名登录 |
| `/api/web3-auth/nonce` | POST | 用户认证 | 获取认证消息 |
| `/api/web3-auth/update-username` | POST | 用户认证 | 更新用户名 |




## API开发优先级排序

基于后端开发最佳实践、依赖关系和业务流程，将36个API接口按开发优先级分为四个阶段：

### 第一阶段：核心基础（必须最先实现）

**优先级1 - 认证基础**
1. `POST /api/web3-auth/nonce` - 获取认证消息
2. `POST /api/web3-auth/login` - 验证签名登录
3. `GET /api/user/me` - 获取用户信息

**理由**:
- 所有其他接口都依赖于用户认证
- 必须先建立用户身份验证机制
- 用户信息是后续所有业务逻辑的基础

**优先级2 - 国际化支持**
4. `GET /locales/{locale}.json` - 加载语言文件

**理由**:
- 前端应用启动必需
- 独立于业务逻辑，可并行开发
- 为后续接口的多语言响应做准备

### 第二阶段：基础功能（核心业务功能）

**优先级3 - 钱包资源管理（游戏核心）**
5. `GET /api/wallet/offline-reward` - 获取离线奖励
6. `POST /api/wallet/claim-offline-reward` - 领取离线奖励
7. `POST /api/wallet/increase-milk` - 增加牛奶
8. `POST /api/wallet/increase-gem` - 增加宝石
9. `POST /api/wallet/batch-update-resources` - 批量更新资源

**理由**:
- 游戏的核心资源系统
- 其他功能模块都会涉及资源变更
- 批量更新接口可提高性能，减少并发问题

**优先级4 - 基础游戏玩法**
10. `GET /api/farm/farm-plots` - 获取农场地块
11. `POST /api/farm/farm-plots/unlock` - 解锁农场地块
12. `POST /api/farm/farm-plots/upgrade` - 升级农场地块
13. `GET /api/delivery/delivery-line` - 获取配送线信息
14. `POST /api/delivery/delivery-line/upgrade` - 升级配送线

**理由**:
- 游戏的主要玩法机制
- 地块解锁是升级的前置条件
- 配送线与农场地块相互关联

**优先级5 - 用户名管理**
15. `POST /api/web3-auth/update-username` - 更新用户名

**理由**:
- 用户体验的基础功能
- 依赖于用户认证系统
- 相对简单，可快速实现

### 第三阶段：扩展功能（增强用户体验）

**优先级6 - 任务系统**
16. `GET /api/tasks` - 获取任务列表
17. `POST /api/tasks/complete` - 完成任务

**理由**:
- 增加用户参与度的重要功能
- 依赖于基础游戏玩法和资源系统
- 任务完成通常会奖励资源

**优先级7 - 推荐系统基础**
18. `GET /api/referral/status` - 获取推荐状态
19. `POST /api/referral/bind` - 绑定推荐码
20. `GET /api/referral/list` - 获取推荐列表

**理由**:
- 用户增长的重要机制
- 推荐状态是其他推荐功能的基础
- 绑定功能需要在用户注册时就可用

**优先级8 - 倒计时宝箱基础**
21. `GET /api/jackpot-chest/countdown` - 获取倒计时宝箱状态
22. `POST /api/jackpot-chest/collect` - 收集倒计时宝箱

**理由**:
- 增加用户留存的核心功能
- 基础的宝箱机制，为后续功能铺垫
- 收集功能依赖于状态查询

**优先级9 - 库存管理**
23. `POST /api/fragment/craft-ticket` - 使用碎片制作门票

**理由**:
- 游戏内物品转换机制
- 依赖于资源系统
- 为门票转移功能做准备

### 第四阶段：高级功能（可选的高级功能）

**优先级10 - 应用内购买核心**
24. `GET /api/iap/store/products` - 获取商店产品
25. `POST /api/iap/payment/create` - 创建支付

**理由**:
- 商业化的核心功能
- 需要完整的支付流程和安全机制
- 依赖于前面的所有基础功能

**优先级11 - 增强道具系统**
26. `GET /api/iap/boosters` - 获取用户增强道具
27. `GET /api/iap/boosters/active` - 获取激活的增强道具
28. `POST /api/iap/boosters/use` - 使用增强道具

**理由**:
- 依赖于商店和支付系统
- 影响游戏平衡，需要仔细设计
- 使用功能依赖于获取功能

**优先级12 - 推荐系统高级功能**
29. `GET /api/referral/referral-chests/count` - 获取推荐宝箱数量
30. `POST /api/invite/claim-daily` - 领取每日推荐宝箱
31. `POST /api/referral/open` - 打开推荐宝箱
32. `GET /api/referral/downline` - 获取下线列表

**理由**:
- 依赖于基础推荐系统
- 涉及复杂的层级关系计算
- 每日奖励需要定时任务支持

**优先级13 - 倒计时宝箱高级功能**
33. `POST /api/jackpot-chest/accelerate` - 加速倒计时宝箱
34. `POST /api/telegram-share/boost` - 触发提升链接

**理由**:
- 依赖于基础宝箱系统和增强道具
- 涉及第三方平台集成（Telegram）
- 社交分享机制较为复杂

**优先级14 - 高级功能**
35. `GET /api/iap/vip/status` - 获取VIP会员状态
36. `GET /api/iap/purchase/history` - 获取购买历史
37. `GET /api/gem-leaderboard` - 获取宝石排行榜
38. `GET /api/free-ticket/remaining-limit` - 获取免费门票剩余限制
39. `POST /api/free-ticket/transfer` - 转移门票