{"paramValidation": "Parameter validation failed", "invalidNonce": "Invalid nonce", "noTokenProvided": "No token provided", "invalidTokenFormat": "Invalid token format", "unauthorized": "Unauthorized", "walletNotFound": "Wallet not found", "userNotFound": "User not found", "insufficientBalance": "Insufficient balance", "transactionFailed": "Transaction failed", "invalidAmount": "Invalid amount", "invalidAddress": "Invalid address", "chestOpenCountInvalid": "Invalid chest open count", "notEnoughChests": "Not enough chests. Required: {required}, Available: {available}", "dailyLimitReached": "Daily limit reached", "taskNotFound": "Task not found", "taskAlreadyCompleted": "Task already completed", "invalidTaskType": "Invalid task type", "serverError": "Internal server error", "loginFailed": "<PERSON><PERSON> failed", "userInfoFailed": "Failed to get user information", "missingWalletId": "Missing wallet ID"}