import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User, UserWallet } from '../models';

@Module({
  imports: [
    SequelizeModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database');
        return {
          dialect: 'mysql',
          host: dbConfig.host,
          port: dbConfig.port,
          username: dbConfig.username,
          password: dbConfig.password,
          database: dbConfig.database,
          models: [User, UserWallet], // 明确指定模型
          autoLoadModels: false, // 关闭自动加载，使用明确指定的模型
          synchronize: dbConfig.synchronize,
          logging: dbConfig.logging ? console.log : false,
          timezone: dbConfig.timezone,
          dialectOptions: dbConfig.dialectOptions,
          define: dbConfig.define,
          pool: dbConfig.pool,
          retry: dbConfig.retry,
        };
      },
      inject: [ConfigService],
    }),
    // 为每个模型注册 SequelizeModule.forFeature
    SequelizeModule.forFeature([User, UserWallet]),
  ],
  exports: [SequelizeModule],
})
export class DatabaseModule {}
