import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Observable } from 'rxjs';

@Injectable()
export class WalletAuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();

    try {
      // 从 headers 里取 Authorization
      const authHeader = request.headers.authorization;

      if (!authHeader) {
        throw new UnauthorizedException('No token provided');
      }

      // Bearer <token>
      const token = authHeader.split(' ')[1];

      if (!token) {
        throw new UnauthorizedException('Invalid token format');
      }

      // 验证 token - 使用钱包专用的JWT密钥
      const jwtSecret = this.configService.get('JWT_SECRET_Wallet') ||
                       this.configService.get('app.jwt.secret');

      const payload = this.jwtService.verify(token, { secret: jwtSecret });

      // 将用户信息添加到请求对象
      request.user = {
        userId: payload.userId,
        walletId: payload.walletId,
        walletAddress: payload.walletAddress,
      };

      return true;
    } catch (error) {
      throw new UnauthorizedException('Unauthorized');
    }
  }
}
