import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Web3SignTestController } from './web3-sign-test.controller';
import { Web3SignTestService } from './web3-sign-test.service';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    ConfigModule,
    CommonModule,
  ],
  controllers: [Web3SignTestController],
  providers: [Web3SignTestService],
  exports: [Web3SignTestService],
})
export class Web3SignTestModule {}
