# Kaia价格更新任务

## 概述

这个定时任务负责定期从Kaiascan API获取Kaia的实时价格，并更新`IapProduct`表中所有产品的`priceKaia`字段。

## 文件说明

### 1. `kaiapriceUpdateWorker.ts`
- **功能**: BullMQ Worker，处理价格更新任务
- **主要功能**:
  - 从Kaiascan API获取Kaia价格 (coin_usd字段)
  - 计算所有产品的Kaia价格: `priceKaia = priceUsd / coin_usd`
  - 更新数据库中的`priceKaia`字段

### 2. `scheduleKaiaPriceUpdateJob.ts`
- **功能**: 任务调度器，设置定时执行
- **默认执行频率**: 每30分钟执行一次
- **可配置**: 通过环境变量`KAIA_PRICE_UPDATE_SCHEDULE`自定义CRON表达式

## API接口

**Kaiascan API**: `https://mainnet-oapi.kaiascan.io/api?module=stats&action=coinprice&apikey=625b664e-c431-442c-9648-b4a9bb5d3a3c`

**返回格式**:
```json
{
  "status": "1",
  "message": "OK",
  "result": {
    "coin_btc": "0.0000010530812266605229",
    "coin_usd": "0.11427507890006554"
  }
}
```

## 价格计算公式

```
priceKaia = priceUsd / coin_usd
```

**示例**:
- 如果产品价格为 `priceUsd = 10.00 USD`
- 当前Kaia价格为 `coin_usd = 0.11427507890006554`
- 则 `priceKaia = 10.00 / 0.11427507890006554 ≈ 87.5123 KAIA`

## 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `KAIA_PRICE_UPDATE_SCHEDULE` | `"0 */30 * * * *"` | CRON表达式，控制任务执行频率 |

### CRON表达式示例

- `"0 */30 * * * *"` - 每30分钟执行一次
- `"0 */5 * * * *"` - 每5分钟执行一次（测试用）
- `"0 0 */1 * * *"` - 每小时执行一次
- `"0 0 */6 * * *"` - 每6小时执行一次

## 启动方式

任务会在应用启动时自动调度，相关代码在 `app.ts` 中:

```typescript
// 调度Kaia价格更新任务
try {
  await scheduleKaiaPriceUpdateJob();
  console.log("Kaia价格更新任务已调度");
} catch (err) {
  console.error("调度Kaia价格更新任务失败：", err);
}
```

## 日志输出

任务执行时会输出详细的日志信息:

```
[KaiaPriceUpdateWorker] 正在从Kaiascan API获取Kaia价格...
[KaiaPriceUpdateWorker] 获取到Kaia价格: 1 KAIA = 0.11427507890006554 USD
[KaiaPriceUpdateWorker] 开始更新所有IapProduct的priceKaia字段...
[KaiaPriceUpdateWorker] 找到 5 个需要更新的产品
[KaiaPriceUpdateWorker] 产品 Premium Booster (ID: 1) 价格已更新: 9.99 USD = 87.4123 KAIA
[KaiaPriceUpdateWorker] 价格更新完成，共更新了 5 个产品
```

## 错误处理

- **API请求失败**: 自动重试最多3次，使用指数退避策略
- **数据库更新失败**: 记录错误日志，继续处理其他产品
- **价格数据无效**: 跳过无效数据，记录错误日志

## 监控和维护

1. **查看任务状态**: 通过BullMQ Dashboard或日志监控任务执行情况
2. **手动触发**: 可以通过BullMQ接口手动触发价格更新
3. **停止任务**: 使用 `removeKaiaPriceUpdateJob()` 函数停止定时任务

## 注意事项

1. **API限制**: 注意Kaiascan API的调用频率限制
2. **网络超时**: 设置了10秒的请求超时时间
3. **数据精度**: priceKaia字段保留4位小数
4. **并发控制**: Worker设置为同时只处理一个任务，避免并发冲突