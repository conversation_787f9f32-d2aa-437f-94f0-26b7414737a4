// /src/scheduler/dailySessions.ts
import cron from "node-cron";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import Session from "../models/Session";
import Round from "../models/Round";

// 扩展 dayjs 功能
dayjs.extend(utc);
dayjs.extend(timezone);

// 设置默认时区（例如：Asia/Shanghai）
const TZ = "Asia/Shanghai";

export const createDailySessions = async () => {
  console.log("[DailySessions] 立刻执行预生成当天 Session 记录...");

  try {
    // 使用 dayjs 获取当天日期（时区为 Asia/Shanghai）
    const today = dayjs().tz(TZ);
    const year = today.year();
    const month = today.month(); // dayjs 的 month 为 0 ~ 11
    const day = today.date();

    // 构造当天 12:00:00 与 20:00:00 的时间对象（使用 dayjs.tz）
    const session08 = dayjs.tz(new Date(year, month, day, 12, 0, 0), TZ);
    const session20 = dayjs.tz(new Date(year, month, day, 20, 0, 0), TZ);

    const sessionsToCreate = [
      {
        session_category: "12:00:00",
        session_dt: session08.toDate(),
      },
      {
        session_category: "20:00:00",
        session_dt: session20.toDate(),
      },
    ];

    console.log(sessionsToCreate);
    

    for (const config of sessionsToCreate) {
      // 检查当天该时段的 Session 是否已存在
      const existingSession = await Session.findOne({
        where: {
          session_category: config.session_category,
          session_dt: config.session_dt,
        },
      });

      if (!existingSession) {
        // 创建 Session 记录
        const newSession = await Session.create({
          session_category: config.session_category,
          session_dt: config.session_dt,
          reservedCount: 0,
          status: "pending",
        });
        console.log(
          `[DailySessions] 创建 Session: ${newSession.id} (${newSession.session_category} ${newSession.session_dt})`
        );

        // 为该 Session 生成 3 个 Round 记录
        // 每个 Round 的 result_time 为 session_dt 后依次增加 10 分钟
        for (let i = 1; i <= 3; i++) {
          const roundTime = dayjs(config.session_dt).add(i * 10, "minute");
          
          await Round.create({
            sessionId: newSession.id,
            roundIndex: i,
            result: "", // 初始结果为空
            status: "pending", // 初始状态为 pending
            result_time: roundTime.toDate(), // 开奖时间
            round_id: `ROUND ${i} - 1`,
            room_status: "open", // 初始房间状态 open
            room_count: 0, // 默认 -1 表示未统计
            // room_id: null, // 初始主房间 ID 为空
            // all_room_id: [], // 初始所有房间 ID 数组为空
          });
          console.log(
            `[DailySessions] 为 Session ${
              newSession.id
            } 创建 Round ${i} (开奖时间: ${roundTime.format()})`
          );
        }
      } else {
        console.log(
          `[DailySessions] Session (${config.session_category} ${dayjs(
            config.session_dt
          ).format()}) 已存在，跳过创建。`
        );
      }
    }
  } catch (error) {
    console.error("[DailySessions] 预生成 Session 记录时出错：", error);
  }
};

/**
 * 每天凌晨 00:00:00 触发，预先生成当天的两个 Session 记录（08:00:00 和 20:00:00）
 * 及每个 Session 下的 6 个 Round 记录。
 *
 * - Session 表字段包括：session_category、session_dt、reservedCount、status
 * - Round 表字段包括：sessionId、roundIndex、result、status、result_time、round_id、room_status、room_count、room_id、all_room_id
 *
 * 每个 Round 的 result_time 为对应 Session 的开始时间后依次增加 10 分钟。
 */
export const scheduleDailySessions = () => {
  // 每天凌晨 00:05 触发任务
  cron.schedule(
    "0 0 * * *",
    async () => {
      console.log("[DailySessions] 开始预生成当天 Session 记录...");
      await createDailySessions();
    },
    {
      scheduled: true,
      timezone: TZ, // 设置任务执行的时区
    }
  );

  createDailySessions();
};
