#!/usr/bin/env node

/**
 * 测试数据库字段
 * 检查 UserWallet 表的实际字段名
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testDatabaseFields() {
  console.log('🔍 测试数据库字段...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过');
    console.log('');

    // 测试一个简单的查询来看错误信息
    console.log('2. 测试 Web3Auth 健康检查...');
    const web3HealthResponse = await axios.get(`${BASE_URL}/web3-auth/health`);
    console.log('✅ Web3Auth 健康检查通过');
    console.log('Redis 状态:', web3HealthResponse.data.data.redis);
    console.log('');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testDatabaseFields().catch(console.error);
