import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  // 数据库连接配置
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'wolf_fun',
  dialect: 'mysql',
  
  // 连接池配置
  pool: {
    max: 10,
    min: 0,
    acquire: 60000,
    idle: 10000,
  },
  
  // 方言选项
  dialectOptions: {
    charset: 'utf8mb4',
    dateStrings: true,
    typeCast: true,
    connectTimeout: 60000,
  },
  
  // 模型定义选项
  define: {
    charset: 'utf8mb4',
    underscored: false, // 修复：数据库字段使用camelCase，不是snake_case
    freezeTableName: true,
    timestamps: true,
  },
  
  // 重试配置
  retry: {
    max: 3,
    match: [
      /Deadlock/i,
      /Connection acquire timeout/i,
    ],
  },
  
  // 时区配置
  timezone: '+08:00',
  
  // 日志配置
  logging: process.env.NODE_ENV === 'development',
  
  // 同步配置（生产环境应为false）
  synchronize: false,
}));
