const axios = require('axios');
const { ethers } = require('ethers');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

async function testRealSignature() {
  try {
    console.log('🧪 测试真实签名的Web3登录...\n');

    // 使用环境变量中的测试私钥
    const testPrivateKey = '0x740fe33880d587c4f2a2f1c0190f10ebe0a74afd6073a08da8d78c3c8a7cd39d';
    const wallet = new ethers.Wallet(testPrivateKey);
    const walletAddress = wallet.address;
    
    console.log(`测试钱包地址: ${walletAddress}`);
    console.log('使用真实的私钥生成签名\n');

    // 步骤1: 获取nonce
    console.log('1. 获取nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: walletAddress
    });

    if (!nonceResponse.data.ok) {
      throw new Error(`获取nonce失败: ${nonceResponse.data.message}`);
    }

    const { nonce, message } = nonceResponse.data.data;
    console.log(`✅ 获取nonce成功: ${nonce.substring(0, 8)}...`);
    console.log(`消息: ${message.substring(0, 50)}...\n`);

    // 步骤2: 生成真实的签名
    console.log('2. 生成真实签名...');
    const signature = await wallet.signMessage(message);
    
    console.log(`✅ 签名生成成功: ${signature.substring(0, 20)}...\n`);

    // 步骤3: 测试登录
    console.log('3. 测试登录...');
    
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: walletAddress,
      signature: signature,
      message: message,
      referralCode: undefined
    });

    if (loginResponse.data.ok) {
      console.log('✅ 登录成功！');
      console.log('响应数据:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
      
      // 检查是否是新用户还是现有用户
      const userId = loginResponse.data.data.user.id;
      console.log(`\n用户ID: ${userId}`);
      
    } else {
      console.log('❌ 登录失败:');
      console.log(JSON.stringify(loginResponse.data, null, 2));
    }

    // 步骤4: 再次测试登录（测试现有用户登录）
    console.log('\n4. 再次测试登录（现有用户）...');
    
    // 重新获取nonce
    const nonceResponse2 = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: walletAddress
    });
    
    const { nonce: nonce2, message: message2 } = nonceResponse2.data.data;
    const signature2 = await wallet.signMessage(message2);
    
    const loginResponse2 = await axios.post(`${BASE_URL}/login`, {
      walletAddress: walletAddress,
      signature: signature2,
      message: message2,
      referralCode: undefined
    });

    if (loginResponse2.data.ok) {
      console.log('✅ 现有用户登录成功！');
      console.log('用户ID:', loginResponse2.data.data.user.id);
      console.log('用户名:', loginResponse2.data.data.user.username);
    } else {
      console.log('❌ 现有用户登录失败:');
      console.log(JSON.stringify(loginResponse2.data, null, 2));
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testRealSignature();
