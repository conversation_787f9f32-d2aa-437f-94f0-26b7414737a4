import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { sequelize } from "../config/db";
import { User, UserWallet, PrizePool, RewardClaim, GameHistory } from "../models";
import dayjs from "dayjs";
import { Op } from "sequelize";
import fs from 'fs';
import path from 'path';
import BigNumber from 'bignumber.js';

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[PersonalKolRewardWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[PersonalKolRewardWorker] ${message}`, ...args);
  },
};

console.log('personalKolRewardWorker.ts loaded');

// 子奖池配置
export const KOL_POOLS = {
  one_star: {
    minAmount: 20000,
    maxAmount: 50000,
    sharePercentage: 0.5
  },
  two_star: {
    minAmount: 50000,
    maxAmount: 80000,
    sharePercentage: 0.5
  },
  three_star: {
    minAmount: 80000,
    maxAmount: Infinity,
    sharePercentage: 0.5
  }
};

async function backupRewardClaims(subPool: string) {
  const currentDate = dayjs().format('YYYY-MM-DD');
  const backupDir = path.join(__dirname, '../../backups/reward_claims');
  
  // 确保备份目录存在
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // 获取所有奖励记录
  const claims = await RewardClaim.findAll({
    where: {
      subPool: `personal_kol_${subPool}`
    }
  });

  // 写入备份文件
  const backupPath = path.join(backupDir, `personal_kol_${subPool}_${currentDate}.json`);
  await fs.promises.writeFile(
    backupPath,
    JSON.stringify(claims, null, 2)
  );

  logger.info(`${subPool} 奖励记录已备份到: ${backupPath}`);
}

// 获取用户一级直推的上周游戏量
export async function getUserDirectReferralGameAmount(userId: number): Promise<number> {
  // 获取上周的开始和结束时间
  const lastWeekStart = dayjs().subtract(1, 'week').startOf('week').toDate();
  const lastWeekEnd = dayjs().subtract(1, 'week').endOf('week').toDate();
  
  // 获取用户的所有一级直推
  const directReferrals = await User.findAll({
    where: {
      referrerId: userId
    }
  });
  
  if (directReferrals.length === 0) {
    return 0;
  }
  
  // 获取所有直推用户的ID
  const referralUserIds = directReferrals.map(user => user.id);
  
  // 计算这些用户上周的游戏总量
  const gameHistories = await GameHistory.findAll({
    where: {
      userId: {
        [Op.in]: referralUserIds
      },
      createdAt: {
        [Op.between]: [lastWeekStart, lastWeekEnd]
      }
    }
  });
  
  // 计算总游戏金额
  const totalGameAmount = gameHistories.reduce((sum, history) => {
    // 假设每场游戏消耗100 USD
    return sum + 100;
  }, 0);
  
  return totalGameAmount;
}

// 分配个人KOL奖励
async function distributePersonalKolReward() {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 获取个人KOL奖池
    const kolPool = await PrizePool.findOne({
      where: { type: 'personal_kol' },
      transaction,
      lock: true
    });

    if (!kolPool || kolPool.amount <= 0) {
      logger.info('奖池为空或金额为0，跳过本次分发');
      await transaction.commit();
      return;
    }

    // 2. 计算每个子奖池的金额
    const totalPoolAmount = kolPool.amount;
    const oneStarPoolAmount = totalPoolAmount * (KOL_POOLS.one_star.sharePercentage / 100);
    const twoStarPoolAmount = totalPoolAmount * (KOL_POOLS.two_star.sharePercentage / 100);
    const threeStarPoolAmount = totalPoolAmount * (KOL_POOLS.three_star.sharePercentage / 100);

    // 3. 获取所有用户
    const users = await User.findAll({
      transaction
    });

    // 4. 计算每个用户的一级直推游戏量并分类
    const oneStarKols: Array<{userId: number, gameAmount: number}> = [];
    const twoStarKols: Array<{userId: number, gameAmount: number}> = [];
    const threeStarKols: Array<{userId: number, gameAmount: number}> = [];

    for (const user of users) {
      const gameAmount = await getUserDirectReferralGameAmount(user.id);
      
      // 根据游戏量分配到不同的子奖池
      if (gameAmount >= KOL_POOLS.three_star.minAmount) {
        threeStarKols.push({ userId: user.id, gameAmount });
      } else if (gameAmount >= KOL_POOLS.two_star.minAmount) {
        twoStarKols.push({ userId: user.id, gameAmount });
      } else if (gameAmount >= KOL_POOLS.one_star.minAmount) {
        oneStarKols.push({ userId: user.id, gameAmount });
      }
    }

    // 5. 分配一星KOL奖池
    await distributeSubPoolRewards(oneStarKols, oneStarPoolAmount, kolPool.id, 'one_star', transaction);
    
    // 6. 分配二星KOL奖池
    await distributeSubPoolRewards(twoStarKols, twoStarPoolAmount, kolPool.id, 'two_star', transaction);
    
    // 7. 分配三星KOL奖池
    await distributeSubPoolRewards(threeStarKols, threeStarPoolAmount, kolPool.id, 'three_star', transaction);

    // 8. 更新奖池余额
    await kolPool.update({ amount: 0 }, { transaction });

    // 9. 备份奖励记录
    await backupRewardClaims('one_star');
    await backupRewardClaims('two_star');
    await backupRewardClaims('three_star');

    await transaction.commit();
    logger.info(`本周个人KOL奖励记录已更新，总金额: ${totalPoolAmount} USD`);

  } catch (error) {
    await transaction.rollback();
    logger.error('奖励记录更新失败:', error);
    throw error;
  }
}

// 分配子奖池奖励
async function distributeSubPoolRewards(
  kols: Array<{userId: number, gameAmount: number}>,
  poolAmount: number,
  prizePoolId: number,
  subPoolName: string,
  transaction: any
) {
  if (kols.length === 0 || poolAmount <= 0) {
    logger.info(`${subPoolName} 奖池没有符合条件的KOL或奖池金额为0，跳过分配`);
    return;
  }

  // 计算总游戏量
  const totalGameAmount = kols.reduce((sum, kol) => new BigNumber(sum).plus(kol.gameAmount).toNumber(), 0);

  // 按比例分配奖励
  for (const kol of kols) {
    const wallet = await UserWallet.findOne({
      where: { userId: kol.userId },
      transaction
    });

    if (!wallet) {
      logger.info(`未找到用户 ${kol.userId} 的钱包，跳过奖励分配`);
      continue;
    }

    // 计算该KOL应得的奖励
    const kolShare = kol.gameAmount / totalGameAmount;
    const rewardAmount = poolAmount * kolShare;

    // 查找是否存在未领取的奖励记录
    const existingClaim = await RewardClaim.findOne({
      where: {
        userId: kol.userId,
        walletId: wallet.id,
        prizePoolId: prizePoolId,
        subPool: `personal_kol_${subPoolName}`,
        claimed: false
      },
      transaction
    });

    if (existingClaim) {
      // 更新现有记录
      await existingClaim.update({
        amount: new BigNumber(existingClaim.amount).plus(rewardAmount).toNumber()
      }, { transaction });
      
      logger.info(`更新用户 ${kol.userId} 的 ${subPoolName} 奖励记录，新增金额: ${rewardAmount} USD`);
    } else {
      // 创建新记录
      await RewardClaim.create({
        userId: kol.userId,
        walletId: wallet.id,
        prizePoolId: prizePoolId,
        subPool: `personal_kol_${subPoolName}`,
        amount: rewardAmount,
        claimed: false
      }, { transaction });

      logger.info(`为用户 ${kol.userId} 创建新的 ${subPoolName} 奖励记录，金额: ${rewardAmount} USD`);
    }
  }
}

// Worker 配置
export const personalKolRewardWorker = new Worker(
  "personal-kol-reward-job",
  async (job: Job) => {
    logger.info(`开始处理任务 ID: ${job.id}`);
    await distributePersonalKolReward();
    logger.info(`任务 ID: ${job.id} 已完成`);
  },
  {
    connection: redis,
    concurrency: 1,
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 },
  }
);

// 错误处理
personalKolRewardWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

personalKolRewardWorker.on("error", (error: Error) => {
  logger.error("Worker 出错:", error);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('个人KOL奖励工作进程收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await personalKolRewardWorker.close();
      
      logger.info('个人KOL奖励工作进程资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('个人KOL奖励工作进程清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on("SIGTERM", async () => {
  logger.info("收到 SIGTERM 信号，关闭 Worker...");
  try {
    await personalKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 监听 SIGINT 信号
process.on("SIGINT", async () => {
  logger.info("收到 SIGINT 信号，关闭 Worker...");
  try {
    await personalKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 处理未捕获的异常
process.on("uncaughtException", async (error) => {
  logger.error("未捕获的异常:", error);
  try {
    await personalKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 处理未处理的 Promise 拒绝
process.on("unhandledRejection", async (reason) => {
  logger.error("未处理的 Promise 拒绝:", reason);
  try {
    await personalKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化个人 KOL 奖励处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return true;
}

// 导出 worker 实例
export default personalKolRewardWorker;