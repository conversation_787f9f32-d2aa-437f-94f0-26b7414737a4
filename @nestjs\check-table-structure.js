#!/usr/bin/env node

/**
 * 检查数据库表结构
 */

const { Sequelize } = require('sequelize');

// 数据库配置（从环境变量获取）
require('dotenv').config();

const sequelize = new Sequelize({
  dialect: 'mysql',
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3669,
  username: process.env.DB_USER || 'wolf',
  password: process.env.DB_PASS || '00321zixunadmin',
  database: process.env.DB_NAME || 'wolf',
  logging: console.log
});

async function checkTableStructure() {
  try {
    console.log('🔍 检查 user_wallets 表结构...\n');

    // 查询表结构
    const [results] = await sequelize.query('DESCRIBE user_wallets');
    
    console.log('user_wallets 表字段:');
    console.table(results);

    console.log('\n🔍 检查 users 表结构...\n');

    // 查询 users 表结构
    const [userResults] = await sequelize.query('DESCRIBE users');
    
    console.log('users 表字段:');
    console.table(userResults);

    await sequelize.close();
  } catch (error) {
    console.error('❌ 检查表结构失败:', error.message);
    process.exit(1);
  }
}

checkTableStructure();
