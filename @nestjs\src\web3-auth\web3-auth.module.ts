import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { Web3AuthController } from './web3-auth.controller';
import { Web3AuthService } from './web3-auth.service';
import { Web3AuthValidationPipe } from './pipes/web3-auth-validation.pipe';
import { RedisModule } from '../redis/redis.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    RedisModule,
    CommonModule,
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '60d' },
    }),
  ],
  controllers: [Web3AuthController],
  providers: [
    Web3AuthService,
    Web3AuthValidationPipe,
  ],
  exports: [Web3AuthService],
})
export class Web3AuthModule {}
