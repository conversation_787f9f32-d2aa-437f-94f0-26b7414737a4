// i18n 功能测试脚本
const axios = require('axios');

async function testI18n() {
  console.log('🌍 测试 i18n 多语言功能...\n');
  
  const baseURL = 'http://localhost:3001/api/web3-auth';
  
  // 测试不同语言的错误消息
  const languages = [
    { code: 'en', name: '英语' },
    { code: 'zh', name: '中文' },
    { code: 'zh-tw', name: '繁体中文' },
    { code: 'ja', name: '日语' }
  ];
  
  for (const lang of languages) {
    console.log(`\n📝 测试语言: ${lang.name} (${lang.code})`);
    console.log('-'.repeat(40));
    
    try {
      // 1. 测试参数验证错误
      console.log('1. 测试参数验证错误...');
      await axios.post(`${baseURL}/nonce`, {
        // 缺少 walletAddress
      }, {
        headers: {
          'Accept-Language': lang.code,
          'Content-Type': 'application/json'
        }
      });
      console.log('❌ 应该返回验证错误');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 参数验证错误正确返回');
        console.log(`   错误消息: ${error.response.data.message}`);
        console.log(`   状态码: ${error.response.status}`);
      } else {
        console.log('❌ 验证错误处理不正确');
      }
    }
    
    try {
      // 2. 测试查询参数语言设置
      console.log('2. 测试查询参数语言设置...');
      await axios.post(`${baseURL}/nonce?lang=${lang.code}`, {
        // 缺少 walletAddress，测试查询参数是否生效
      }, {
        headers: {
          'Accept-Language': 'en', // 设置不同的 Accept-Language
          'Content-Type': 'application/json'
        }
      });
      console.log('❌ 应该返回验证错误');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 查询参数语言设置生效');
        console.log(`   错误消息: ${error.response.data.message}`);
      } else {
        console.log('❌ 查询参数语言设置无效');
      }
    }
  }
  
  console.log('\n🎉 i18n 功能测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  testI18n().catch(console.error);
}

module.exports = { testI18n };
