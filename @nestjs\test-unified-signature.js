const { ethers } = require('ethers');
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/web3-auth';

// 测试统一签名验证
async function testUnifiedSignatureVerification() {
  console.log('🔐 测试统一签名验证逻辑');
  console.log('=====================================');

  // 测试1: 真实钱包签名验证
  console.log('\n📝 测试1: 真实钱包签名验证');
  await testRealWalletSignature();

  // 测试2: 之前的"测试钱包"现在也需要签名验证
  console.log('\n📝 测试2: 之前的测试钱包现在也需要签名验证');
  await testFormerTestWallet();
}

async function testRealWalletSignature() {
  try {
    // 生成随机钱包
    const wallet = ethers.Wallet.createRandom();
    const walletAddress = wallet.address;
    const privateKey = wallet.privateKey;

    console.log(`钱包地址: ${walletAddress}`);
    console.log(`私钥: ${privateKey}`);

    // 1. 获取 nonce
    console.log('\n1. 获取 nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: walletAddress
    });

    if (!nonceResponse.data.ok) {
      throw new Error(`获取 nonce 失败: ${nonceResponse.data.message}`);
    }

    const nonce = nonceResponse.data.data.nonce;
    const message = nonceResponse.data.data.message;
    console.log(`✅ 获取 nonce 成功: ${nonce}`);
    console.log(`消息: ${message.substring(0, 50)}...`);

    // 2. 签名消息
    console.log('\n2. 签名消息...');
    const signature = await wallet.signMessage(message);
    console.log(`✅ 签名成功: ${signature.substring(0, 20)}...`);

    // 3. 本地验证签名
    console.log('\n3. 本地验证签名...');
    const recoveredAddress = ethers.verifyMessage(message, signature);
    console.log(`恢复的地址: ${recoveredAddress}`);
    console.log(`原始地址: ${walletAddress}`);
    console.log(`验证结果: ${recoveredAddress.toLowerCase() === walletAddress.toLowerCase() ? '✅ 成功' : '❌ 失败'}`);

    // 4. 登录
    console.log('\n4. 登录...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      walletAddress: walletAddress,
      signature: signature,
      message: message
    });

    console.log('登录响应:');
    console.log(JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.ok) {
      console.log('✅ 真实钱包登录成功！');
      console.log(`用户ID: ${loginResponse.data.data.user.id}`);
      console.log(`用户名: ${loginResponse.data.data.user.username}`);
      console.log(`钱包地址: ${loginResponse.data.data.user.walletAddress}`);
      console.log(`Token: ${loginResponse.data.data.token.substring(0, 50)}...`);
    } else {
      console.log('❌ 真实钱包登录失败');
    }

  } catch (error) {
    console.log(`❌ 真实钱包测试过程中发生错误: ${error.message}`);
    if (error.response?.data) {
      console.log('错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function testFormerTestWallet() {
  try {
    // 使用之前的"测试钱包"地址，但现在需要提供真实的签名
    const testWalletAddress = '******************************************';
    
    console.log(`测试钱包地址: ${testWalletAddress}`);
    console.log('⚠️  注意：这个钱包现在也需要真实的签名验证');

    // 1. 获取 nonce
    console.log('\n1. 获取 nonce...');
    const nonceResponse = await axios.post(`${BASE_URL}/nonce`, {
      walletAddress: testWalletAddress
    });

    if (!nonceResponse.data.ok) {
      throw new Error(`获取 nonce 失败: ${nonceResponse.data.message}`);
    }

    const nonce = nonceResponse.data.data.nonce;
    const message = nonceResponse.data.data.message;
    console.log(`✅ 获取 nonce 成功: ${nonce}`);

    // 2. 尝试使用无效签名登录（应该失败）
    console.log('\n2. 尝试使用无效签名登录（应该失败）...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/login`, {
        walletAddress: testWalletAddress,
        signature: '0xinvalidsignature',
        message: message
      });

      if (loginResponse.data.ok) {
        console.log('❌ 意外成功：测试钱包不应该能够使用无效签名登录');
      } else {
        console.log('✅ 预期失败：测试钱包无法使用无效签名登录');
        console.log(`失败原因: ${loginResponse.data.message}`);
      }
    } catch (error) {
      console.log('✅ 预期失败：测试钱包无法使用无效签名登录');
      console.log(`失败原因: ${error.response?.data?.message || error.message}`);
    }

    console.log('\n📋 总结：');
    console.log('- 之前的"测试钱包"地址现在也需要真实的签名验证');
    console.log('- 无法再通过简单的钱包地址匹配跳过签名验证');
    console.log('- 所有钱包地址都必须提供有效的签名才能登录');

  } catch (error) {
    console.log(`❌ 测试钱包测试过程中发生错误: ${error.message}`);
    if (error.response?.data) {
      console.log('错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testUnifiedSignatureVerification().catch(console.error);
