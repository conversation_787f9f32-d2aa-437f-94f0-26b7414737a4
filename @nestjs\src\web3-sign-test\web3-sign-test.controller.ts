import {
  <PERSON>,
  Post,
  Body,
  HttpStatus,
  Logger,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { Web3SignTestService } from './web3-sign-test.service';
import { SignMessageDto } from './dto/sign-message.dto';

import { TranslationService } from '../common/services/translation.service';
import { SupportedLanguage } from '../i18n/i18n.service';

@Controller('web3-sign-test')
export class Web3SignTestController {
  private readonly logger = new Logger(Web3SignTestController.name);

  constructor(
    private readonly web3SignTestService: Web3SignTestService,
    private readonly translationService: TranslationService,
  ) {}

  /**
   * 使用服务器私钥签名消息
   * 路径: /api/web3-sign-test/sign
   * 方法: POST
   */
  @Post('sign')
  async signMessage(
    @Body() signMessageDto: SignMessageDto,
    @Req() req: Request,
  ) {
    const startTime = Date.now();
    const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';
    const language = req.headers['accept-language'] || 'en';

    try {
      this.logger.log(`Web3签名请求，客户端: ${clientIP}, 消息长度: ${signMessageDto.message?.length || 0}`);

      // 调用服务进行签名
      const result = await this.web3SignTestService.signMessage(signMessageDto);
      
      const duration = Date.now() - startTime;
      this.logger.log(`Web3签名成功，钱包地址: ${result.address}, 客户端: ${clientIP}, 耗时: ${duration}ms`);
      
      // 直接返回业务数据，让全局拦截器包装
      return {
        signature: result.signature,
        address: result.address,
        message: result.message
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`Web3签名失败，客户端: ${clientIP}, 耗时: ${duration}ms`, error);
      
      // 根据错误类型返回不同的错误消息和状态码
      let errorMessage: string;
      let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

      if (error.message === 'TEST_ETH_PRIVATE_KEY not set in environment variables') {
        errorMessage = this.translationService.t(
          'errors.serverError',
          {},
          this.getLanguageFromHeader(language)
        );
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      } else {
        errorMessage = this.translationService.t(
          'errors.serverError',
          {},
          this.getLanguageFromHeader(language)
        );
      }
      
      throw new HttpException({
        ok: false,
        message: errorMessage,
        error: error.message
      }, statusCode);
    }
  }

  /**
   * 从Accept-Language头解析语言
   * @param acceptLanguage Accept-Language头值
   * @returns 支持的语言代码
   */
  private getLanguageFromHeader(acceptLanguage: string): SupportedLanguage {
    if (!acceptLanguage) return 'en';
    
    const lang = acceptLanguage.toLowerCase();
    
    if (lang.includes('zh-tw') || lang.includes('zh-hk')) return 'zh-tw';
    if (lang.includes('zh')) return 'zh';
    if (lang.includes('ja')) return 'ja';
    
    return 'en';
  }
}
