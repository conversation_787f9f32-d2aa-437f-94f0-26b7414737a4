import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface ShareBoostLinkAttributes {
  id: number;
  userId: number;
  walletId: number;
  chestId: number; // 关联的宝箱ID
  chestLevel: number; // 宝箱等级
  code: string; // 唯一助力码
  maxUses: number; // 最大使用次数
  currentUses: number; // 当前使用次数
  expiresAt: Date; // 过期时间
  createdAt?: Date;
  updatedAt?: Date;
}

type ShareBoostLinkCreationAttributes = Optional<ShareBoostLinkAttributes, "id">;

export class ShareBoostLink
  extends Model<ShareBoostLinkAttributes, ShareBoostLinkCreationAttributes>
  implements ShareBoostLinkAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public chestId!: number;
  public chestLevel!: number;
  public code!: string;
  public maxUses!: number;
  public currentUses!: number;
  public expiresAt!: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

ShareBoostLink.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    chestId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    chestLevel: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    maxUses: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 12,
      allowNull: false,
    },
    currentUses: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 0,
      allowNull: false,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: "share_boost_links",
    sequelize,
    timestamps: true,
  }
);