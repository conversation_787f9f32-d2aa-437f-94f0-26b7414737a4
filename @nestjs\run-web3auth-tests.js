// Web3Auth模块测试运行脚本
const { spawn } = require('child_process');
const path = require('path');

async function runTests() {
  console.log('🧪 运行Web3Auth模块单元测试...\n');
  
  return new Promise((resolve, reject) => {
    // 运行Jest测试，只测试web3-auth模块
    const testProcess = spawn('npm', ['run', 'test', '--', '--testPathPattern=web3-auth'], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ 所有测试通过！');
        resolve();
      } else {
        console.log(`\n❌ 测试失败，退出码: ${code}`);
        reject(new Error(`Tests failed with exit code ${code}`));
      }
    });
    
    testProcess.on('error', (error) => {
      console.error('❌ 测试运行错误:', error);
      reject(error);
    });
  });
}

async function runCoverage() {
  console.log('\n📊 生成测试覆盖率报告...\n');
  
  return new Promise((resolve, reject) => {
    // 运行Jest测试覆盖率
    const coverageProcess = spawn('npm', ['run', 'test:cov', '--', '--testPathPattern=web3-auth'], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    });
    
    coverageProcess.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ 覆盖率报告生成完成！');
        resolve();
      } else {
        console.log(`\n⚠️  覆盖率报告生成失败，退出码: ${code}`);
        resolve(); // 不阻止主流程
      }
    });
    
    coverageProcess.on('error', (error) => {
      console.error('⚠️  覆盖率报告生成错误:', error);
      resolve(); // 不阻止主流程
    });
  });
}

async function main() {
  try {
    console.log('🚀 Web3Auth模块测试套件');
    console.log('='.repeat(50));
    
    // 检查是否在正确的目录
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    try {
      require(packageJsonPath);
    } catch (error) {
      console.error('❌ 请在@nestjs目录中运行此脚本');
      process.exit(1);
    }
    
    // 运行单元测试
    await runTests();
    
    // 生成覆盖率报告
    await runCoverage();
    
    console.log('\n🎉 Web3Auth模块测试完成！');
    console.log('\n📋 测试总结:');
    console.log('  ✅ Web3AuthService 单元测试');
    console.log('  ✅ Web3AuthController 单元测试');
    console.log('  ✅ 错误处理测试');
    console.log('  ✅ Redis连接测试');
    console.log('  ✅ 验证管道测试');
    
    console.log('\n📁 相关文件:');
    console.log('  📄 src/web3-auth/web3-auth.service.spec.ts');
    console.log('  📄 src/web3-auth/web3-auth.controller.spec.ts');
    console.log('  📄 coverage/ (覆盖率报告)');
    
  } catch (error) {
    console.error('\n❌ 测试运行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTests, runCoverage };
