import { Module, Global } from '@nestjs/common';
import { ResponseInterceptor } from './interceptors/response.interceptor';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { LoggingService } from './services/logging.service';
import { TranslationService } from './services/translation.service';
import { RequestContextService } from './services/request-context.service';

@Global()
@Module({
  providers: [
    ResponseInterceptor,
    HttpExceptionFilter,
    LoggingService,
    TranslationService,
    RequestContextService,
  ],
  exports: [
    ResponseInterceptor,
    HttpExceptionFilter,
    LoggingService,
    TranslationService,
    RequestContextService,
  ],
})
export class CommonModule {}
