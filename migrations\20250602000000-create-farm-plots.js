'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('farm_plots')) {
      await queryInterface.createTable('farm_plots', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      plotNumber: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      level: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 1,
      },
      barnCount: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 1,
      },
      milkProduction: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 1,
      },
      productionSpeed: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 5, // 5秒/次
      },
      unlockCost: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 2000, // 初始解锁费用
      },
      upgradeCost: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 200, // 初始升级费用
      },
      lastProductionTime: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      isUnlocked: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      accumulatedMilk: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 添加联合唯一索引，确保每个用户的每个牧场区编号唯一
    await queryInterface.addIndex('farm_plots', ['userId', 'plotNumber'], {
      unique: true,
      name: 'farm_plots_user_plot_unique',
    });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('farm_plots');
  },
};