# Web3 Sign Test 接口设置指南

## 快速设置

### 1. 生成测试私钥

如果您没有测试用的以太坊私钥，可以使用以下方法生成一个：

#### 方法1: 使用 ethers.js (推荐)
```javascript
// 在 Node.js 环境中运行
const { ethers } = require('ethers');

// 生成随机钱包
const wallet = ethers.Wallet.createRandom();

console.log('私钥:', wallet.privateKey);
console.log('地址:', wallet.address);
console.log('助记词:', wallet.mnemonic.phrase);
```

#### 方法2: 使用在线工具
- 访问 https://vanity-eth.tk/ 生成测试钱包
- **注意**: 仅用于测试，不要用于生产环境

### 2. 设置环境变量

在 `@nestjs/.env` 文件中添加：

```bash
# Web3 测试配置
TEST_ETH_PRIVATE_KEY=0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12
```

**重要提醒**:
- ⚠️ 这个私钥仅用于测试目的
- ⚠️ 不要在生产环境中使用
- ⚠️ 不要向测试钱包转入真实资金

### 3. 验证设置

运行测试脚本验证配置：

```bash
cd @nestjs
node test-web3-sign-test.js
```

## 完整测试流程

### 1. 启动应用
```bash
npm run start:dev
```

### 2. 测试签名接口
```bash
# PowerShell (推荐)
Invoke-WebRequest -Uri "http://localhost:3001/api/web3-sign-test/sign" -Method POST -ContentType "application/json" -Body '{"message":"Hello, Web3 World!"}' -Headers @{"Accept-Language"="zh-CN"}

# curl (如果可用)
curl -X POST http://localhost:3001/api/web3-sign-test/sign \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh-CN" \
  -d '{"message":"Hello, Web3 World!"}'
```

### 3. 预期响应
```json
{
  "ok": true,
  "data": {
    "signature": "0x...",
    "address": "0x...",
    "message": "Hello, Web3 World!"
  }
}
```

## 故障排除

### 问题1: "TEST_ETH_PRIVATE_KEY not set"
**解决方案**: 
1. 检查 `.env` 文件是否存在
2. 确认环境变量名称正确
3. 重启应用

### 问题2: "Invalid private key"
**解决方案**:
1. 确认私钥格式正确（以0x开头，64位十六进制）
2. 使用有效的以太坊私钥

### 问题3: 签名验证失败
**解决方案**:
1. 检查消息内容是否正确
2. 确认使用的是同一个私钥
3. 验证签名格式

## 安全最佳实践

### 开发环境
- ✅ 使用专门的测试私钥
- ✅ 不要使用真实资金的钱包
- ✅ 定期轮换测试私钥

### 生产环境
- ❌ 不要在生产环境中使用此接口
- ❌ 不要在代码中硬编码私钥
- ❌ 不要将私钥提交到版本控制

## 接口文档

### 请求
- **URL**: `/api/web3-sign-test/sign`
- **方法**: POST
- **Content-Type**: application/json

### 请求体
```json
{
  "message": "要签名的消息内容"
}
```

### 响应
#### 成功响应
```json
{
  "ok": true,
  "data": {
    "signature": "0x签名结果",
    "address": "0x钱包地址",
    "message": "原始消息"
  }
}
```

#### 错误响应
```json
{
  "ok": false,
  "message": "错误消息",
  "error": "详细错误信息"
}
```

## 多语言支持

接口支持以下语言的错误消息：
- 🇺🇸 英语 (en)
- 🇨🇳 中文简体 (zh)
- 🇹🇼 中文繁体 (zh-tw)
- 🇯🇵 日语 (ja)

通过 `Accept-Language` 请求头指定语言：
```bash
curl -H "Accept-Language: zh-CN" ...
```

## 技术支持

如果遇到问题，请检查：
1. 应用日志输出
2. 环境变量配置
3. 网络连接状态
4. 私钥格式正确性

更多技术细节请参考 `WEB3_SIGN_TEST_REFACTOR.md` 文档。
