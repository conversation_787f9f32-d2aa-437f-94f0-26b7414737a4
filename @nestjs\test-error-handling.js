// 错误处理测试脚本
const axios = require('axios');

async function testErrorHandling() {
  console.log('🔍 测试Web3Auth错误处理机制...\n');
  
  const baseURL = 'http://localhost:3001/api/web3-auth';
  
  // 测试用例
  const testCases = [
    {
      name: '1. 测试缺少walletAddress参数',
      data: {},
      expectedStatus: 400,
      expectedErrorType: 'validation'
    },
    {
      name: '2. 测试空的walletAddress',
      data: { walletAddress: '' },
      expectedStatus: 400,
      expectedErrorType: 'validation'
    },
    {
      name: '3. 测试null walletAddress',
      data: { walletAddress: null },
      expectedStatus: 400,
      expectedErrorType: 'validation'
    },
    {
      name: '4. 测试非字符串walletAddress',
      data: { walletAddress: 123 },
      expectedStatus: 400,
      expectedErrorType: 'validation'
    },
    {
      name: '5. 测试额外的无关参数',
      data: { 
        walletAddress: '******************************************',
        extraParam: 'should be ignored'
      },
      expectedStatus: 200,
      expectedErrorType: null
    },
    {
      name: '6. 测试正常的钱包地址',
      data: { walletAddress: '******************************************' },
      expectedStatus: 200,
      expectedErrorType: null
    }
  ];
  
  // 测试不同语言的错误消息
  const languages = ['en', 'zh', 'zh-tw', 'ja'];
  
  for (const lang of languages) {
    console.log(`\n📝 测试语言: ${lang}`);
    console.log('='.repeat(50));
    
    for (const testCase of testCases) {
      try {
        console.log(`\n${testCase.name}`);
        
        const response = await axios.post(`${baseURL}/nonce`, testCase.data, {
          headers: {
            'Content-Type': 'application/json',
            'Accept-Language': lang
          }
        });
        
        if (testCase.expectedStatus === 200) {
          console.log('✅ 请求成功');
          console.log(`   状态码: ${response.status}`);
          console.log(`   响应格式: ${response.data.ok ? '正确' : '错误'}`);
          
          if (response.data.data && response.data.data.nonce) {
            console.log(`   Nonce: ${response.data.data.nonce.substring(0, 8)}...`);
          }
        } else {
          console.log('❌ 应该返回错误，但请求成功了');
          console.log(`   实际状态码: ${response.status}`);
        }
        
      } catch (error) {
        if (error.response) {
          const actualStatus = error.response.status;
          const responseData = error.response.data;
          
          if (actualStatus === testCase.expectedStatus) {
            console.log('✅ 错误处理正确');
            console.log(`   状态码: ${actualStatus}`);
            console.log(`   错误格式: ${responseData.ok === false ? '正确' : '错误'}`);
            console.log(`   错误消息: ${responseData.message}`);
            
            if (responseData.error && Array.isArray(responseData.error)) {
              console.log(`   验证错误详情: ${responseData.error.length} 个错误`);
              responseData.error.forEach((err, index) => {
                console.log(`     ${index + 1}. 字段: ${err.field}, 消息: ${err.message}`);
              });
            }
          } else {
            console.log('❌ 状态码不匹配');
            console.log(`   期望: ${testCase.expectedStatus}, 实际: ${actualStatus}`);
          }
        } else {
          console.log('❌ 网络错误:', error.message);
        }
      }
    }
  }
  
  // 测试Content-Type错误
  console.log('\n\n📝 测试Content-Type错误处理');
  console.log('='.repeat(50));
  
  try {
    const response = await axios.post(`${baseURL}/nonce`, 
      'invalid json', 
      {
        headers: {
          'Content-Type': 'text/plain',
          'Accept-Language': 'en'
        }
      }
    );
    console.log('❌ 应该返回Content-Type错误，但请求成功了');
  } catch (error) {
    if (error.response) {
      console.log('✅ Content-Type错误处理正确');
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误消息: ${error.response.data.message || 'No message'}`);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
  
  console.log('\n🎉 错误处理测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  testErrorHandling().catch(console.error);
}

module.exports = { testErrorHandling };
