// src/config/db.ts
import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

export const sequelize = new Sequelize(
  process.env.DB_NAME || "wolf_fun_db",
  process.env.DB_USER || "root",
  process.env.DB_PASS || "root",
  {
    host: process.env.DB_HOST || "localhost",
    dialect: "mysql",
    logging:false,
    port: Number(process.env.DB_PORT) || 3669,
    timezone: '+08:00',
    pool: {
      max: 10,
      min: 0,
      acquire: 60000,
      idle: 10000
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
      connectTimeout: 60000
    },
    define: {
      charset: "utf8mb4",
      collate: "utf8mb4_unicode_ci",
      timestamps: true,
    },
    retry: {
      max: 3,
      match: [
        /Deadlock/i,
        /Connection acquire timeout/i
      ]
    }
  }
);

export async function connectDB() {
  try {
    await sequelize.sync();
    sequelize.authenticate()
    .then(() => {
      console.log('数据库连接成功');
    })
    .catch(err => {
      console.error('数据库连接失败:', err);
      // 尝试重新连接
      setTimeout(() => {
        sequelize.authenticate();
      }, 5000);
    });

    // 使用 sequelize 实例的 beforeDisconnect 钩子来监听断开连接事件
    Sequelize.beforeDisconnect(() => {
      console.log('数据库连接断开，尝试重连');
      sequelize.authenticate();
    });
    
  
    console.log("MySQL connection has been established successfully.");
  } catch (error) {
    console.error("Unable to connect to the database:", error);
    throw error;
  }
}
